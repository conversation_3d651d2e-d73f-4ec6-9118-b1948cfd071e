import { createApp } from 'vue';
import App from './App.vue';
import { router } from './router';
import './style.css';

// ARCO UI
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import '@arco-design/web-vue/dist/arco.less';

// ELEMENT PLUS
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';

// 公共组件
// @ts-ignore
import CsCommonComponents from 'cs-common-components';
import 'cs-common-components/dist/style.css';

// EPG 组件
import EpgJsView from '@epgui/epg-jsview-mock';
import EpgComponents from '@epgui/epg-components';

// @ts-ignore
// import EpgV6Components from '@epgui/epgui-v6'

// 依赖样式
import '@chances/iam_portal/dist/style.css';
import '@chances/iam_portal/dist/iam_theme.less';
import '@chances/audit_publish_portal/dist/style.css';
import '@chances/audit_publish_portal/dist/audit_publish_portal_theme.less';
import '@epgui/epg-components/dist/style.css';
import '@epgui/epg-jsview-mock/dist/style.css';

// 公共依赖包
import {
    createPermissionDirective,
    createPersistPlugin,
    emitter,
    installAllStores,
    STORAGE_DRIVER,
} from '@chances/portal_common_core';
import '@chances/portal_common_core/dist/style.css';

import { createPinia } from 'pinia';

// 注册公共状态
import installCommonStores from '@smartdesk/common/stores';

// 注册公共组件
import installCommonComponents from '@smartdesk/common/components';

// 初始化
import { init } from '@smartdesk/main/utils/init.ts';

// 创建 APP 实例
const app: any = createApp(App);

// 创建 pinia 持久化插件
const persistPlugin = createPersistPlugin({
    defaultDatabase: 'smartdesk-portal',
    defaultDriver: STORAGE_DRIVER.LOCAL_STORAGE,
    timeout: 10000,
});

// 注册 pinia
const pinia = createPinia();
app.use(pinia);

// 注册持久化插件
app.use(persistPlugin);

// 权限指令
app.directive('permission', createPermissionDirective());

// 注册公共依赖 store
installAllStores(pinia as any);

// 注册可视化公共 store
installCommonStores(pinia);

// ARCO UI
app.use(ArcoVue).use(ArcoVueIcon);

// ElementPlus
app.use(ElementPlus);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}

// 公共组件 UI
app.use(CsCommonComponents);

// EPG UI
app.use(EpgComponents, { prefix: 'Epgui' });
app.use(EpgJsView);
// app.use(EpgV6Components, { prefix: 'Epgui' });

// 注册公共组件
app.use(installCommonComponents);

// 将 Mitt 实例注入到全局属性中
app.config.globalProperties.$emitter = emitter;

// 注册路由
app.use(router);

// 初始化
init().then(() => {
    // 挂载实例
    app.mount('#app');
});
