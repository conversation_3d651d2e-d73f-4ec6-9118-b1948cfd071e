import { enumApi } from '@smartdesk/common/api';
import { useEnumStore } from '@chances/portal_common_core';

/**
 * 加载可视化枚举
 */
export const loadVisualEnums = async () => {
    const res = await enumApi.treeEnum({});
    if (res.code === 200) {
        useEnumStore().addEnumsList(res.result);
    }
};

/**
 * 加载 CMS 枚举
 */
export const loadCmsEnums = async () => {
    const enumParam = {
        codes: ['vodDubEnum', 'countryEnum', 'yearEnum', 'linkTypeEnum', 'pointContentTypeEnum'],
    };
    const res = await enumApi.searchCmsEnum(enumParam);
    if (res.code === 200) {
        useEnumStore().addEnumsList(res.result);
    }
};

/**
 * 初始化函数
 */
export const init = async () => {
    useEnumStore().enums = [];
    // 可视化的枚举必须同步加载
    await loadVisualEnums();

    // CMS 的枚举可异步加载
    loadCmsEnums();
};
