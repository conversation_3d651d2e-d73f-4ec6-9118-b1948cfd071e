import { createRouter, create<PERSON>eb<PERSON>istory, RouteRecordRaw } from 'vue-router';
import { useMenuStore, useOperatorStore } from '@chances/portal_common_core';
import { routes as preview_routes } from '@smartdesk/preview/router';
import { routes as design_routes } from '@smartdesk/design/router';
import { routes as admin_routes } from '@smartdesk/admin/router';

// @ts-ignore
import { routes as iam_routes } from '@chances/iam_portal';
// @ts-ignore
import { routes as audit_publish_routes } from '@chances/audit_publish_portal';

//  动态路由
const dynamicRoutes: RouteRecordRaw[] = [
    ...preview_routes,
    ...design_routes,
    ...admin_routes,
    ...iam_routes,
    ...audit_publish_routes,
];

// 基座项目路由
const baseRoutes: RouteRecordRaw[] = [
    // iframe 组件路由
    {
        path: '/iframe',
        name: 'BaseIframe',
        component: () => import('../components/base-iframe.vue'),
        props: (route) => ({ url: route.query.url as string }),
        children: [],
    },
    // Forbidden 组件路由
    {
        path: '/forbidden',
        name: 'Forbidden',
        component: () => import('../views/Forbidden.vue'),
    },
    // Login 组件路由
    {
        path: '/login',
        name: 'Login',
        meta: {
            componentName: 'Login',
            layout: 'designer',
        },
        component: () => import('../views/login/index.vue'),
    },
];

const whiteList = ['/iframe', '/forbidden', '/login'];

export const router = createRouter({
    history: createWebHistory(),
    routes: [...dynamicRoutes, ...baseRoutes],
});

router.beforeEach((to, from, next) => {
    // 校验是否登录，未登录则路由到登录页
    if (to.path !== '/login' && !useOperatorStore().getOperator().userId) {
        next({ path: '/login' });
        return;
    }

    // 处理根路径重定向
    if (to.path === '/' && useOperatorStore().getOperator().userId) {
        const menus = useMenuStore().getMenus() ?? [];
        const [firstMenu = {} as any] = menus;
        const [firstChildMenu = {}] = firstMenu.children ?? [];

        if (firstChildMenu.path) {
            next({ path: firstChildMenu.path });
            return;
        } else {
            // 若不存在子菜单路径，默认处理
            next();
            return;
        }
    }

    // 检查菜单权限
    if (Object.keys(useMenuStore().menuMap).includes(to.path) || whiteList.includes(to.path)) {
        next();
    } else {
        next({ path: '/forbidden' });
    }
});
