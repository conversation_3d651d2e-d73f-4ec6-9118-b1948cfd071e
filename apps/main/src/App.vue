<template>
    <router-view v-slot="{ Component }">
        <keep-alive :include="tabViewStore.cachedViews">
            <component :is="getLayoutWrappedComponent(Component)" :key="route.fullPath" />
        </keep-alive>
    </router-view>
</template>

<script setup lang="ts">
    import { defineComponent, getCurrentInstance, h } from 'vue';
    import { useRoute } from 'vue-router';
    import SiderHeaderContentLayout from './layouts/sider-header-content-layout.vue';
    import DesignerLayout from './layouts/designer.vue';
    import { useTabViewStore } from '@smartdesk/main/stores';

    // pinia store
    const route = useRoute();
    const tabViewStore = useTabViewStore();

    // 布局与子页面包装器
    const createWrapper = (layoutComponent: any, component: any) => {
        const componentName = (route.meta?.name as string) || component.type.name;

        return defineComponent({
            name: componentName,
            setup(_) {
                const instance: any = getCurrentInstance();

                // 如果需要缓存，额外处理 ctx（针对 admin 布局的特定 hack）
                if (instance && instance.ctx) {
                    instance.ctx.activate = () => {
                        // 空实现，防止 keep-alive 报错
                    };
                    instance.ctx.deactivate = () => {
                        // 空实现，防止 keep-alive 报错
                    };
                }

                return () =>
                    h(
                        layoutComponent,
                        {},
                        {
                            default: () => h(component),
                        }
                    );
            },
        });
    };

    // 获取布局包装组件 - 根据当前布局创建包装组件
    const getLayoutWrappedComponent = (component: any) => {
        if (!component) return null;

        // 根据当前布局返回包装后的组件
        if (route.meta?.layout === 'designer') {
            // 设计器布局：没有导航
            return createWrapper(DesignerLayout, component);
        }

        // 管理布局，带导航
        return createWrapper(SiderHeaderContentLayout, component);
    };
</script>
