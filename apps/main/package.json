{"name": "@smartdesk/main", "version": "1.0.0", "description": "可视化基座模块", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "vite --config vite.config.ts", "build": "vue-tsc && vite build --config vite.config.ts", "dev:lib": "vite --config vite.lib.config.ts", "build:lib": "vue-tsc && vite build --config vite.lib.config.ts", "type-check": "vue-tsc --build"}, "dependencies": {"@arco-design/web-vue": "^2.57.0", "@chances/audit_publish_portal": "1.0.20250624152356", "@chances/iam_portal": "1.0.20250714094938", "@element-plus/icons-vue": "^2.3.1", "@epgui/epg-commons": "1.0.4", "@epgui/epg-components": "1.0.4", "@epgui/epg-jsview-mock": "1.0.4", "@epgui/epg-meta": "1.0.4", "@epgui/epgui-v6": "1.0.202507301511", "@smartdesk/admin": "workspace:*", "@smartdesk/common": "workspace:*", "@smartdesk/design": "workspace:*", "@smartdesk/preview": "workspace:*", "axios": "^1.9.0", "crypto-js": "^4.2.0", "cs-common-components": "^0.0.8", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "element-plus": "^2.9.10", "mitt": "^3.0.1"}, "devDependencies": {"@iconify-json/mdi": "^1.2.3", "@types/node": "^24.0.14", "@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "less": "^4.3.0", "less-loader": "^12.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5.5.3", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-dts": "^4.0.0", "vue-tsc": "^2.0.0"}, "peerDependencies": {"@chances/portal_common_core": "1.0.20250619141115", "pinia": "2.3.1", "vue": "3.5.13", "vue-router": "4.5.0"}}