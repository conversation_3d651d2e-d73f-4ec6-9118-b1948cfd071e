import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResultResponse } from '@chances/portal_common_core';
import { Desktop, DesktopSearchForm, Page, PaginationParams } from '@smartdesk/common/types';

export interface DesktopApi {
    /**
     * 查询所有桌面列表
     *
     * @param searchForm 查询表单
     * @param paginationParams 分页参数
     * */
    getDesktopList(
        searchForm: Partial<DesktopSearchForm>,
        paginationParams: PaginationParams
    ): Promise<RestPageResultResponse<Desktop>>;

    // 新增桌面
    createDesktop(data: Partial<Page>): Promise<RestResultResponse<Page>>;

    // 修改桌面
    updateDesktop(code: string, data: Partial<Page>): Promise<RestResultResponse<Page>>;

    // 分页多条件查询桌面列表
    findDesktopList(
        searchForm: Partial<DesktopSearchForm>,
        paginationParams: PaginationParams
    ): Promise<RestPageResultResponse<Page>>;
}

export const desktopApi: DesktopApi = {
    /**
     * 查询所有桌面列表
     *
     * @param searchForm 查询表单
     * @param paginationParams 分页参数
     * */
    getDesktopList(
        searchForm: Partial<DesktopSearchForm>,
        paginationParams: PaginationParams
    ): Promise<RestPageResultResponse<Desktop>> {
        return smartDeskHttpClient.post('/desktop/list', searchForm).params(paginationParams).send();
    },

    // 新增桌面
    createDesktop(data: Partial<Page>): Promise<RestResultResponse<Page>> {
        return smartDeskHttpClient.post('/desktop', data).send();
    },

    // 修改桌面
    updateDesktop(code: string, data: Partial<Page>): Promise<RestResultResponse<Page>> {
        return smartDeskHttpClient.post(`/desktop/${code}`, data).send();
    },

    // 分页多条件查询桌面列表
    findDesktopList(
        searchForm: Partial<DesktopSearchForm>,
        paginationParams: PaginationParams
    ): Promise<RestPageResultResponse<Page>> {
        return smartDeskHttpClient.post('/desktop/list').data(searchForm).params(paginationParams).send();
    },
};
