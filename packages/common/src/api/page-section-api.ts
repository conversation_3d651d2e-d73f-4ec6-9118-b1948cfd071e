import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { PageSection, PageSectionSearchForm, PaginationParams, Section } from '@smartdesk/common/types';

export interface PageSectionApi {
    /**
     * 分页多条件查询页面楼层列表
     *
     * @param searchForm 页面楼层查询表单
     * @param pageInfo 分页参数
     * @return 页面楼层列表
     */
    findPageSectionList(
        searchForm: Partial<PageSectionSearchForm>,
        pageInfo: Partial<PaginationParams>
    ): Promise<RestPageResultResponse<PageSection>>;

    /**
     * 查询页面楼层信息
     *
     * @param pageSectionCode 页面楼层编码
     */
    getPageSectionByCode(pageSectionCode: string): Promise<RestResultResponse<PageSection>>;

    /**
     * 创建页面楼层
     *
     * @param pageCode 页面编码
     * @param section 楼层定义
     * @param index 索引
     * */
    createPageSection(pageCode: string, section: Section, index: number): Promise<RestResultResponse<PageSection>>;

    /**
     * 修改页面楼层数据
     *
     * @param code 页面楼层 code
     * @param pageSectionData 页面楼层数据
     */
    updatePageSectionData(code: string, pageSectionData: PageSection): Promise<RestResultResponse<PageSection>>;

    /**
     * 修改页面楼层样式
     *
     * @param code 页面楼层 code
     * @param pageSectionData 页面楼层数据
     * */
    updatePageSectionLayout(code: string, pageSectionData: PageSection): Promise<RestResultResponse<PageSection>>;

    /**
     * 修改页面楼层排序
     * */
    updatePageSectionOrder(data: Partial<PageSection>[]): Promise<RestResponse>;

    /**
     * 更换页面楼层
     * */
    changePageSection(sourceCode: string, targetCode: string): Promise<RestResultResponse<PageSection>>;

    /**
     * 新增页面楼层
     * */
    addPageSection(sourceCode: string, targetCode: string): Promise<RestResultResponse<PageSection>>;

    /**
     * 另存为楼层定义
     * */
    saveAsSection(pageSectionCode: string, sectionForm: Section): Promise<RestResponse>;
}

export const pageSectionApi: PageSectionApi = {
    /**
     * 分页多条件查询页面楼层列表
     *
     * @param searchForm 页面楼层查询表单
     * @param pageInfo 分页参数
     * @return 页面楼层列表
     */
    findPageSectionList(
        searchForm: Partial<PageSectionSearchForm>,
        pageInfo: Partial<PaginationParams>
    ): Promise<RestPageResultResponse<PageSection>> {
        return smartDeskHttpClient.post(`/page/section/list`).data(searchForm).params(pageInfo).send();
    },

    /**
     * 查询页面楼层信息
     *
     * @param pageSectionCode 页面楼层编码
     */
    getPageSectionByCode(pageSectionCode: string): Promise<RestResultResponse<PageSection>> {
        return smartDeskHttpClient.get(`/page/section`).params({ code: pageSectionCode }).send();
    },

    /**
     * 创建页面楼层
     *
     * @param pageCode 页面编码
     * @param section 楼层定义
     * @param index 索引
     * */
    createPageSection(pageCode: string, section: Section, index?: number): Promise<RestResultResponse<PageSection>> {
        return smartDeskHttpClient.post(`/page/section`).params({ pageCode, index }).data(section).send();
    },

    /**
     * 修改页面楼层管理信息
     *
     * @param code 页面楼层 code
     * @param pageSectionData 页面楼层数据
     */
    updatePageSectionData(code: string, pageSectionData: PageSection): Promise<RestResultResponse<PageSection>> {
        return smartDeskHttpClient.post(`/page/section/${code}`, pageSectionData).send();
    },

    /**
     * 修改页面楼层样式
     *
     * @param code 页面楼层 code
     * @param pageSectionData 页面楼层数据
     * */
    updatePageSectionLayout(code: string, pageSectionData: PageSection): Promise<RestResultResponse<PageSection>> {
        return smartDeskHttpClient.post(`/page/section/${code}/layout`, pageSectionData).send();
    },

    /**
     * 修改页面楼层排序
     * */
    updatePageSectionOrder(data: Partial<PageSection>[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/page/section/order`, data).send();
    },

    /**
     * 更换页面楼层
     * */
    changePageSection(sourceCode: string, targetCode: string): Promise<RestResultResponse<PageSection>> {
        return smartDeskHttpClient.post(`/page/section/change`).params({ sourceCode, targetCode }).send();
    },

    /**
     * 新增页面楼层
     * */
    addPageSection(sourceCode: string, targetCode: string): Promise<RestResultResponse<PageSection>> {
        return smartDeskHttpClient.post(`/page/section/add`).params({ sourceCode, targetCode }).send();
    },

    /**
     * 另存为楼层定义
     * */
    saveAsSection(pageSectionCode: string, sectionForm: Section): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/page/section/${pageSectionCode}/save_section`).data(sectionForm).send();
    },
};
