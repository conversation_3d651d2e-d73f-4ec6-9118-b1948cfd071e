import type { CmsQueryParam, Nav, Page } from '@smartdesk/common/types';

// 管理状态查询表单
export interface AdminStatusSearchForm {
    // 删除状态
    delFlag: number;

    // 可用状态
    status: number;

    // 删除状态列表
    delFlags: number[];

    // 启用状态列表
    statuses: number[];
}

// 发布状态查询表单
export interface PublishStatusSearchForm extends AdminStatusSearchForm {
    // 审核状态
    auditStatus: number;

    // 上线状态
    onlineStatus: number;

    // 可见状态
    visibleStatus: number;

    // 审核状态列表
    auditStatuses: number[];

    // 上线状态列表
    onlineStatuses: number[];

    // 可见状态列表
    visibleStatuses: number[];
}

// 导航查询表单
export interface NavSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 桌面编码
    desktopCode: string;

    // 页面编码
    pageCode: string;

    // 类型
    type: number;

    // 名称
    title: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];
}

// 导航新增表单
export interface NavForm extends Nav {
    // 关联页面
    page: Partial<Page>;
}

// 导航分组查询表单
export interface NavGroupSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 桌面编码
    desktopCode: string;

    // 名称
    title: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];
}

// 页面楼层查询表单
export interface PageSectionSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 页面编码
    pageCode: string;

    // 楼层定义编码
    sectionCode: string;

    // 父编码
    parentCode: string;

    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];
}

// 查询布局表单
export interface LayoutSearchForm extends AdminStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 布局名称
    name: string;

    // 布局类型
    type: string;

    // 布局编码
    code: string;

    // 布局编码列表
    codes: string[];

    // 标签
    tags: string;

    // 标签列表
    tagsList: string[];
}

/**
 * 查询楼层定义表单
 */
export interface SectionSearchForm extends AdminStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 布局编码
    layoutCode: string;

    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 类型
    type: string;

    // 楼层类型列表
    sectionTypes: string[];

    // 分辨率
    resolution: string;

    // 分辨率列表
    resolutions: string[];

    // 标签
    tags: string;

    // 标签列表
    tagsList: string[];

    // 创建者
    createdBy: string;

    // 作用域
    scope: number;

    // 组织id
    orgIds: number[];
}

// 查询路由配置表单
export interface LinkTypeSearchForm extends AdminStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 名称
    name: string;

    // 布局类型
    dataType: string;

    // 布局编码
    code: string;

    // 布局编码列表
    codes: string[];

    // 组织id
    orgIds: number[];
}

// 查询推荐策略表单
export interface PersonalRuleSearchForm extends AdminStatusSearchForm {
    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 推荐策略目录编码
    folderCode: string;

    // 关键字
    keyword: string;

    // 组织id
    orgIds: number[];
}

export interface EnumParam {
    // codes
    codes: string[];
}

// 网站查询表单
export interface SiteSearchForm extends AdminStatusSearchForm {
    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 数据域
    domain: string;

    // 组织id
    orgIds: number[];
}

// 页面查询表单
export interface PageSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 业务分组
    bizGroups: string[];

    // 标签
    tags: string;

    // 组织id
    orgIds: number[];
}

// 桌面查询表单
export interface DesktopSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 业务分组
    bizGroup: string;

    // 标签
    tags: string;

    // 标签列表
    tagsList: string[];

    // 业务分组列表
    bizGroups: string[];

    // 组织id
    orgIds: number[];
}

/**
 * 查询组件表单
 */
export interface ComponentSearchForm extends AdminStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 组件编码
    code: string;

    // 组件名称
    name: string;

    // 组件编码列表
    codes: string[];

    // 组织id
    orgIds: number[];

    // 组件分类
    componentCategorys: string[];

    // 组件类别
    componentCatalogs: string[];
}

/**
 * 查询组件样式表单
 */
export interface ComponentStyleSearchForm extends AdminStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 组件编码
    componentCode: string;

    // 组件编码列表
    componentCodes: string[];

    // 组件样式编码
    code: string;

    // 组件样式名称
    name: string;

    // 标签
    tags: string;

    // 组件样式编码列表
    codes: string[];
}

// 布局查询表单
export interface LayoutParam extends AdminStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 名称
    name: string;

    // 类型
    layoutTypes: string[];

    // 组织id
    orgIds: number[];
}

/**
 * 内容查询表单
 * */
export interface ContentQueryParam extends CmsQueryParam {
    // 语言（配音语言列表）
    vodDubs: string[];

    // 内容产地（地区过滤）
    areas: string[];

    // 上映年份（年份过滤）
    years: string[];

    // 内容提供商（需特殊处理STAR标识）
    contentCpCodes: string[];

    // 基础类型标签
    contentBastags: string[];

    // 运营标签
    opType: string;

    // 运营标签
    opTags: string[];

    // 资费包编码
    packageCode: string;

    // 内容名称
    name: string;

    // 内容编码
    code: string;

    // 内容类型（vod:单剧集，episode:连续剧子集，series2:系列剧子集，series:连续剧）
    types: string[];

    // 审核人ID
    auditUserId: string;

    // 评分下限
    scoreBegin: number;

    // 评分上限
    scoreEnd: number;

    // 演员（逗号分隔的姓名）
    actors: string;

    // 导演（逗号分隔的姓名）
    directors: string;

    // 节目类型编码
    typeCode: string;
}

/**
 * 链接查询表单
 * */
export interface LinkQueryParam extends CmsQueryParam {
    // 运营组织编码列表
    opCodes: string[];

    // 链接类型分类
    genres: string[];

    // 链接地址（需符合URI规范）
    url: string;

    // 链接显示名称
    name: string;

    // 链接唯一编码
    code: string;
}

// 演职员查询表单
export interface CastQueryParam extends CmsQueryParam {
    // 编码
    code: string;

    // 名称
    name: string;
}

// 专栏查询表单
export interface ColumnQueryParam extends CmsQueryParam {
    // 编码
    code: string;

    // 名称
    name: string;

    // 运营商
    cpCodes: string[];
}

// 专题查询表单
export interface SubjectQueryParam extends CmsQueryParam {
    // 编码
    code: string;

    // 名称
    name: string;

    // 专题类型
    subjectType: string;

    // 运营提供商编码列表（多选过滤）
    cpCodes: string[];
}

// 直播频道查询表单
export interface ChannelQueryParam extends CmsQueryParam {
    // 频道唯一标识编码
    code: string;

    // 频道显示名称
    name: string;

    // 关联的资费包编码
    packageCode: string;

    // 运营提供商编码列表（多选过滤）
    cpCodes: string[];
}

// 直播节目单查询表单
export interface ScheduleQueryParam extends CmsQueryParam {
    // 直播节目单编码
    code: string;

    // 直播节目单名称
    name: string;

    // 直播频道名称
    channelName: string;

    // 开始时间
    startTime: Date;

    // 结束时间
    endTime: Date;
}

// IOP 查询表单
export interface IopQueryParam extends CmsQueryParam {}
// ContentPoint  查询表单
export interface ContentPointQueryParam extends CmsQueryParam {
    // 编码
    code: string;
    // 名称
    name: string;
    // 片段标题
    pointName: string;
    // 片段标识
    pointCode: string;
    // 片段类型
    pointType: string;
}

// 角标查询表单
export interface ShowFlagQueryParam extends CmsQueryParam {
    // 名称
    name: string;

    // 编码
    code: string;

    // 类型
    types: number[];

    // 位置
    positions: number[];
}

// 导航查询表单
export interface NavSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 桌面编码
    desktopCode: string;

    // 页面编码
    pageCode: string;

    // 类型
    type: number;

    // 名称
    title: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 桌面编码列表
    desktopCodes: string[];
}

// 机顶盒设置
export interface DeviceSettingForm {
    // 业务账号
    UserID: string;

    // 机顶盒型号
    STBType: string;

    // 业务账号
    userId: string;

    // 用户分组
    userGroup: string;

    // 用户区域
    areaCode: string;

    // 子区域
    subAreaCode: string;
}

export interface BoxSearchForm extends AdminStatusSearchForm {
    // 名称
    name: string;

    // 编码
    code: string;

    // 机顶盒厂商
    boxGroups: string[];

    // 能力
    capability: string[];
}

// 校验名称表单
export interface ValidateNameForm {
    // 实体类型
    entityType: string;

    // 网站编码
    siteCode: string;

    // 名称
    name: string;

    // 排除的 ID
    excludeId: number;
}

// 校验编码表单
export interface ValidateCodeForm {
    // 实体类型
    entityType: string;

    // 网站编码
    siteCode: string;

    // 编码
    code: string;

    // 排除的 ID
    excludeId: number;
}
