<template>
    <el-dialog v-model="props.dialogVisible" title="选择推荐策略" width="60%" @close="handleClose">
        <el-form label-width="120px" :model="searchForm" :label-suffix="':'" :size="'default'">
            <el-row :gutter="24">
                <el-col :span="12">
                    <el-form-item label="推荐策略目录">
                        <el-cascader
                            v-model="searchForm.folderCode"
                            :options="personalRuleFolderOptions"
                            :props="{
                                label: 'name',
                                value: 'code',
                                checkStrictly: true,
                                emitPath: false,
                            }"
                            clearable
                            placeholder="请选择目录" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="关键字">
                        <el-input placeholder="请输入关键字" clearable v-model="searchForm.keyword">
                            <template #append>
                                <el-button :icon="Search" @click="onSearchData" />
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            v-loading="loading"
            :data="ruleList"
            :style="{ width: '100%' }"
            :row-key="(row: any) => row.code"
            highlight-current-row
            border
            :current-row-key="selectedRow">
            <el-table-column label="选择" width="55">
                <template #default="scope">
                    <el-radio
                        class="hidden-label"
                        v-model="selectedRow"
                        :label="scope.row.code"
                        @change="handleRowSelect(scope.row)">
                    </el-radio>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="策略名称" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                    <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                        {{ enumStore.getLabelByKeyAndValue('enableStatus', row.status) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="citationCount" label="引用次数" width="100" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.citationCount > 0 ? 'success' : 'danger'">
                        {{ row.citationCount }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="createdBy" label="创建人" width="100" />
        </el-table>
        <base-pagination :page-info="pageInfo" @change="handlePageChange" />
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm"> 确定 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { onMounted, reactive, ref, watch } from 'vue';
    import { Search } from '@element-plus/icons-vue';
    import { personalRuleApi, personalRuleFolderApi } from '@smartdesk/common/api';
    import { PageInfo, PaginationParams, PersonalRule, PersonalRuleFolderModel } from '@smartdesk/common/types';
    import { useEnumStore } from '@chances/portal_common_core';
    import { ElMessage } from 'element-plus';

    // 推荐策略弹框
    defineOptions({
        name: 'PersonalRuleDialog',
    });

    // 参数
    const props = defineProps<{
        // 当前选中内容
        modelValue: string;
        dialogVisible: boolean;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: Object): void;
        (e: 'update:dialogVisible', value: boolean): void;
        (e: 'save', vale: Object): void;
    }>();

    const enumStore = useEnumStore();

    // 当前选中行
    const currentRow = ref<PersonalRule>({} as PersonalRule);
    const selectedRow = ref(props.modelValue || '');

    // 加载
    const loading = ref(false);
    const searchForm = ref<any>({});

    // 当前分页信息
    const pageInfo = reactive<PageInfo>({
        page: 1,
        size: 10,
        sizeArray: [10, 20, 50, 100],
        totalElements: 0,
    });

    // 分页参数
    const pageParam = ref<PaginationParams>({} as PaginationParams);

    // table 数据
    const ruleList = ref<PersonalRule[]>([] as PersonalRule[]);

    // 选择款数据
    const personalRuleFolderOptions = ref<PersonalRuleFolderModel[]>([] as PersonalRuleFolderModel[]);

    // 获取目录树
    const getPersonalRuleFolderTree = async () => {
        const res = await personalRuleFolderApi.getPersonalRuleFolderTree(false, '', '');
        personalRuleFolderOptions.value = res.result || [];
    };

    // 处理分页变化
    const handlePageChange = (newPageInfo: PageInfo) => {
        Object.assign(pageInfo, newPageInfo);
        getPersonalRuleList();
    };

    // 行选中事件
    const handleRowSelect = (row: PersonalRule) => {
        currentRow.value = row;
        selectedRow.value = row.code;
    };

    // 获取推荐策略列表
    const getPersonalRuleList = async () => {
        loading.value = true;
        pageParam.value.page = pageInfo.page - 1;
        pageParam.value.size = pageInfo.size;
        const res = await personalRuleApi.getPersonalRules(searchForm.value, pageParam.value);
        if (res.code === 200) {
            ruleList.value = res.result;
            pageInfo.totalElements = Number(res.page.totalElements);
        }
        loading.value = false;
    };

    const onSearchData = () => {
        getPersonalRuleList();
    };

    const handleClose = () => {
        currentRow.value = {} as PersonalRule;
        selectedRow.value = '';
        emit('update:dialogVisible', false);
    };

    // 取消按钮
    const onCancel = () => {
        selectedRow.value = '';
        currentRow.value = {} as PersonalRule;
        emit('update:dialogVisible', false);
    };

    // 确定按钮
    const onConfirm = () => {
        if (!currentRow.value?.code) {
            ElMessage.warning('请选择一条记录');
            return;
        }
        emit('update:modelValue', selectedRow.value);
        emit('save', selectedRow.value);
        emit('update:dialogVisible', false);
    };

    watch(
        () => props.dialogVisible,
        (newVal) => {
            if (newVal) {
                selectedRow.value = props.modelValue;
            }
        }
    );

    onMounted(() => {
        getPersonalRuleFolderTree();
        getPersonalRuleList();
    });
</script>

<style scoped>
    :deep(.hidden-label) .el-radio__label {
        display: none !important;
    }
</style>
