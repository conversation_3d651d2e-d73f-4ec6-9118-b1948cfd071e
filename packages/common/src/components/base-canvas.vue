<template>
    <div class="relative w-full" :style="{ height: canvasHeight }">
        <div ref="horizontalRef" class="horizontal absolute w-full h-[30px]" />
        <div ref="verticalRef" class="vertical absolute w-[30px] h-full" />

        <div class="absolute bottom-5 right-5 flex items-center gap-2 bg-white p-1.5 rounded shadow-md z-[22]">
            <el-button
                @click="resetView"
                icon="Refresh"
                size="small"
                title="重置视图"
                class="flex items-center justify-center px-2 py-1 text-xs border border-gray-200 bg-white rounded hover:bg-gray-50" />
            <div class="h-4 w-[1px] bg-gray-200"></div>
            <el-button
                @mousedown="
                    (e: any) => {
                        if (!e.currentTarget.disabled) zoomLongPress('out');
                    }
                "
                @mouseup="clearZoomLongPress"
                @mouseleave="clearZoomLongPress"
                icon="Minus"
                size="small"
                :disabled="scale <= MIN_SCALE"
                class="w-6 h-6 border border-gray-200 bg-white rounded flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed" />
            <span class="min-w-[48px] text-center text-sm">{{ Math.round(scale * 100) }}%</span>
            <el-button
                @mousedown="
                    (e: any) => {
                        if (!e.currentTarget.disabled) zoomLongPress('in');
                    }
                "
                @mouseup="clearZoomLongPress"
                @mouseleave="clearZoomLongPress"
                icon="Plus"
                size="small"
                :disabled="scale >= MAX_SCALE"
                class="w-6 h-6 border border-gray-200 bg-white rounded flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed" />
        </div>

        <slot name="navigation" />

        <div
            id="base-canvas-container"
            ref="containerRef"
            class="container absolute top-[30px] left-[30px] right-0 bottom-0 overflow-auto bg-white box-border z-10"
            :style="containerStyle">
            <div
                ref="contentRef"
                id="canvas-content"
                class="origin-top-left min-w-full min-h-full transition-transform duration-150 ease-out">
                <slot name="overlay" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, inject, nextTick, onBeforeUnmount, onMounted, provide, ref, watch } from 'vue';
    import Guides from '@scena/guides';

    // 基础画布
    defineOptions({ name: 'BaseCanvas' });

    // 组件属性定义
    const props = withDefaults(
        defineProps<{
            maxRulerSize?: number;
            minScale?: number;
            maxScale?: number;
            zoomStep?: number;
            contentWidth?: number;
            contentHeight?: number;
            canvasHeight?: string;
        }>(),
        {
            maxRulerSize: 999999,
            minScale: 0.5,
            maxScale: 2.4,
            zoomStep: 0.01,
            contentWidth: undefined,
            contentHeight: undefined,
            canvasHeight: '100%',
        }
    );

    // 模板引用
    const containerRef = ref<HTMLElement>();
    const horizontalRef = ref<HTMLElement>();
    const verticalRef = ref<HTMLElement>();
    const contentRef = ref<HTMLElement>();

    // 响应式状态
    const scale = ref<number>(1);
    const rulersReady = ref(false);
    const contentPosition = ref({ x: 0, y: 0 });

    // 依赖注入和计算属性
    const centerWidth = inject('centerWidth', 1200);
    const getCenterWidth = computed(() => (centerWidth as any).value ?? centerWidth);
    const getContentWidth = computed(() => props.contentWidth);
    const MIN_SCALE = computed(() => props.minScale);
    const MAX_SCALE = computed(() => props.maxScale);
    const ZOOM_STEP = computed(() => props.zoomStep);

    // 提供缩放给子组件
    provide('epg_scale', scale);

    // 类型定义
    type ZoomDirection = 'in' | 'out';

    // 标尺实例
    const rulers = {
        horizontal: ref<Guides | null>(null),
        vertical: ref<Guides | null>(null),
    };

    // 标尺配置
    const rulerConfig = {
        displayDragPos: true,
        useResizeObserver: true,
        backgroundColor: '#f5f5f5',
        lineColor: '#ccc',
        textColor: '#666',
        range: [0, props.maxRulerSize] as [number, number],
    };

    // 画布容器样式
    const containerStyle = computed(() => ({
        backgroundImage: `radial-gradient(#d0d3d9 ${1 * scale.value}px, transparent ${1 * scale.value}px)`,
        backgroundSize: `${20 * scale.value}px ${20 * scale.value}px`,
        cursor: 'grab',
    }));

    // 缩放相关状态
    let zoomTimer: ReturnType<typeof setTimeout> | null = null;
    let zoomInterval = 300;

    // 直接更新 DOM 样式的核心函数
    const updateDOMTransform = (x: number, y: number) => {
        const contentElement = document.getElementById('canvas-content');
        if (contentElement) {
            contentElement.style.transform = `scale(${scale.value}) translate(${x}px, ${y}px)`;
            // 强制浏览器重新计算样式
            contentElement.offsetHeight;
        }
    };

    // 同步标尺位置
    const syncRulers = (x: number, y: number) => {
        if (rulersReady.value) {
            nextTick(() => {
                rulers.horizontal.value?.scroll(-x);
                rulers.vertical.value?.scroll(-y);
            });
        }
    };

    // 初始化标尺
    const initRulers = (): void => {
        const checkAndInit = () => {
            if (horizontalRef.value && verticalRef.value) {
                const hRect = horizontalRef.value.getBoundingClientRect();
                const vRect = verticalRef.value.getBoundingClientRect();

                if (hRect.width > 0 && hRect.height > 0 && vRect.width > 0 && vRect.height > 0) {
                    rulers.horizontal.value = new Guides(horizontalRef.value, {
                        ...rulerConfig,
                        type: 'horizontal',
                        zoom: scale.value,
                        rulerStyle: {
                            left: '30px',
                            width: 'calc(100% - 30px)',
                            height: '100%',
                        },
                    });

                    rulers.vertical.value = new Guides(verticalRef.value, {
                        ...rulerConfig,
                        type: 'vertical',
                        zoom: scale.value,
                        rulerStyle: {
                            top: '30px',
                            height: 'calc(100% - 30px)',
                            width: '100%',
                        },
                    });

                    rulersReady.value = true;
                    return true;
                }
            }
            return false;
        };

        if (!checkAndInit()) {
            const tryInit = () => {
                if (!checkAndInit()) {
                    requestAnimationFrame(tryInit);
                }
            };
            requestAnimationFrame(tryInit);
        }
    };

    // 更新标尺缩放
    const updateRulerZoom = (): void => {
        if (rulersReady.value) {
            rulers.horizontal.value?.setState({ zoom: scale.value });
            rulers.vertical.value?.setState({ zoom: scale.value });
        }
    };

    // 缩放相关函数
    const zoom = (direction: ZoomDirection, amount: number = ZOOM_STEP.value): void => {
        const delta = direction === 'in' ? amount : -amount;
        const newScale = Math.min(Math.max(scale.value + delta, MIN_SCALE.value), MAX_SCALE.value);
        scale.value = Number(newScale.toFixed(2));
    };

    const zoomLongPress = (direction: ZoomDirection) => {
        if (
            (direction === 'in' && scale.value >= MAX_SCALE.value) ||
            (direction === 'out' && scale.value <= MIN_SCALE.value)
        )
            return;
        zoom(direction);
        zoomInterval = Math.max(50, zoomInterval - 30);
        zoomTimer = setTimeout(() => zoomLongPress(direction), zoomInterval);
    };

    const clearZoomLongPress = () => {
        if (zoomTimer) clearTimeout(zoomTimer);
        zoomTimer = null;
        zoomInterval = 300;
    };

    // 边界计算函数
    const getActualContentHeight = () => {
        if (!contentRef.value) return 0;
        return contentRef.value.getBoundingClientRect().height;
    };

    const calculateYBounds = () => {
        if (!containerRef.value) return { minY: 0, maxY: 0 };

        const contentHeight = getActualContentHeight() / scale.value;
        if (contentHeight === 0) return { minY: 0, maxY: Infinity };

        const containerHeight = containerRef.value.clientHeight / scale.value;
        const minY = 0;
        const maxY = Math.max(0, contentHeight - containerHeight + 100);
        return { minY, maxY };
    };

    // 应用边界限制
    const applyBounds = (y: number) => {
        const contentHeight = getActualContentHeight();
        if (contentHeight === 0) return y;

        const { minY, maxY } = calculateYBounds();
        let limitedY = Math.min(y, -minY);
        if (maxY !== Infinity) {
            limitedY = Math.max(limitedY, -maxY);
        }
        return limitedY;
    };

    // 更新内容位置并同步标尺（主要的位置更新函数）
    const updatePositionAndRulers = (x: number, y: number) => {
        const limitedY = applyBounds(y);

        // 如果位置没有变化，直接返回
        if (contentPosition.value.x === x && contentPosition.value.y === limitedY) {
            return;
        }

        // 更新响应式数据
        contentPosition.value = { x, y: limitedY };

        // 直接更新 DOM 样式
        updateDOMTransform(x, limitedY);

        // 同步标尺位置
        syncRulers(x, limitedY);
    };

    // 滚轮事件处理
    const handleWheel = (e: WheelEvent): void => {
        e.preventDefault();
        const newY = contentPosition.value.y - e.deltaY;
        const limitedY = applyBounds(newY);

        if (contentPosition.value.y === limitedY) return;

        contentPosition.value = { x: contentPosition.value.x, y: limitedY };
        updateDOMTransform(contentPosition.value.x, limitedY);
        syncRulers(contentPosition.value.x, limitedY);
    };

    // 缩放计算和处理
    const calcScale = (): number => {
        const cw = getCenterWidth.value;
        const contentW = getContentWidth.value;
        if (!contentW) return 1;
        return Math.min(1, cw / contentW);
    };

    const handleScaleChange = (): void => {
        const newScale = calcScale();
        if (newScale !== scale.value) {
            scale.value = newScale;
        }
    };

    // 重置视图
    const resetView = () => {
        handleScaleChange();
        updatePositionAndRulers(0, 0);
    };

    // 事件绑定和解绑
    const bindEventListener = (): void => {
        window.addEventListener('resize', handleScaleChange);
        if (containerRef.value) {
            containerRef.value.addEventListener('wheel', handleWheel, {
                passive: false,
            });
        }
    };

    const unBindEventListener = (): void => {
        window.removeEventListener('resize', handleScaleChange);
        if (containerRef.value) {
            containerRef.value.removeEventListener('wheel', handleWheel);
        }
    };

    // 响应式监听器
    watch([getCenterWidth, getContentWidth], handleScaleChange);

    watch(scale, () => {
        updateRulerZoom();
        // 缩放变化时重新应用边界限制并更新 DOM
        const limitedY = applyBounds(contentPosition.value.y);
        if (limitedY !== contentPosition.value.y) {
            updatePositionAndRulers(contentPosition.value.x, limitedY);
        } else {
            // 即使位置没变，缩放变化时也需要更新 transform
            updateDOMTransform(contentPosition.value.x, contentPosition.value.y);
        }
    });

    watch(rulersReady, (ready) => {
        if (ready && (contentPosition.value.x !== 0 || contentPosition.value.y !== 0)) {
            syncRulers(contentPosition.value.x, contentPosition.value.y);
        }
    });

    watch(
        () => getActualContentHeight(),
        (newHeight, oldHeight) => {
            if (newHeight > 0 && oldHeight === 0 && contentPosition.value.y !== 0) {
                nextTick(() => {
                    updatePositionAndRulers(contentPosition.value.x, contentPosition.value.y);
                });
            }
        }
    );

    // 生命周期钩子
    onMounted(() => {
        nextTick(() => {
            initRulers();
            bindEventListener();
            handleScaleChange();
            updateDOMTransform(contentPosition.value.x, contentPosition.value.y);
        });
    });

    onBeforeUnmount(() => {
        unBindEventListener();
    });

    // 导出方法
    defineExpose({
        updatePositionAndRulers,
    });
</script>

<style scoped>
    :deep(.scena-guides-guide) {
        display: none !important;
    }
</style>
