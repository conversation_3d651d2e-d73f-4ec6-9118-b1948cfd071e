<template>
    <div class="spacing-editor">
        <!-- 输入框区域 -->
        <div class="input-group">
            <label for="top">上:</label>
            <el-input-number
                v-model="localStyle.top"
                @change="changeHandler"
                :placeholder="'上'"
                :aria-label="'Top Position'" />
        </div>
        <div class="input-group">
            <label for="left">左:</label>
            <el-input-number
                @change="changeHandler"
                v-model="localStyle.left"
                :placeholder="'左'"
                :aria-label="'Left Position'" />
        </div>
        <div class="input-group">
            <label for="width">宽:</label>

            <el-input-number
                @change="changeHandler"
                v-model="localStyle.width"
                :placeholder="'宽'"
                :aria-label="'Width'" />
        </div>
        <div class="input-group">
            <label for="height">高:</label>
            <el-input-number
                @change="changeHandler"
                v-model="localStyle.width"
                :placeholder="'宽'"
                :aria-label="'Width'" />
        </div>
        <div class="input-group">
            <label for="height">高:</label>
            <el-input-number
                @change="changeHandler"
                v-model="localStyle.height"
                :placeholder="'高'"
                :aria-label="'Height'" />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { ref } from 'vue';

    interface Rect {
        top: number;
        left: number;
        width: number;
        height: number;
    }

    // 使用 ref 创建响应式数据

    const emit = defineEmits<{
        (e: 'update:modelValue', value: Object): void;
        (e: 'change', value: Object): void;
    }>();

    const props = defineProps<{
        modelValue: {
            type: Rect;
            default: () => {
                top: 0;
                left: 0;
                width: 200;
                height: 100;
            };
        };
    }>();

    const localStyle = ref<Rect>({
        top: (props.modelValue as any)['top'],
        left: (props.modelValue as any)['left'],
        width: (props.modelValue as any)['width'],
        height: (props.modelValue as any)['height'],
    });

    const changeHandler = () => {
        emit('update:modelValue', localStyle.value);
        emit('change', localStyle.value);
    };
</script>

<style scoped>
    .spacing-editor {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px;
        margin-top: 10px;
        width: 300px;
        margin-bottom: 20px;
    }

    .input-group {
        display: flex;
        align-items: center;
        margin-top: 5px;
    }

    label {
        margin-right: 5px;
        font-weight: bold;
    }

    input {
        width: 70px;
        padding: 5px;
        text-align: center;
    }

    .target-element {
        position: absolute; /* 使用绝对定位来移动元素 */
        border: 2px solid #000;
        background-color: lightgray;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .target-element p {
        margin: 0;
    }
</style>
