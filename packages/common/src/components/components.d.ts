import { DefineComponent } from 'vue';

const defineComponent = DefineComponent<{}, {}, any>;

// Vue 3 的全局组件类型扩展
declare module '@vue/runtime-core' {
    export interface GlobalComponents {
        ActionButton: (typeof import('./action-button.vue'))['default'];
        BackgroundEditor: (typeof import('./background-editor.vue'))['default'];
        BaseCanvas: (typeof import('./base-canvas.vue'))['default'];
        BaseCheckbox: (typeof import('./base-checkbox.vue'))['default'];
        BaseDatePicker: (typeof import('./base-date-picker.vue'))['default'];
        BaseDesigner: (typeof import('./base-designer.vue'))['default'];
        BaseModal: (typeof import('./base-modal.vue'))['default'];
        BasePagination: (typeof import('./base-pagination.vue'))['default'];
        BaseTableWithPagination: (typeof import('./base-table-with-pagination.vue'))['default'];
        BaseTopBar: (typeof import('./base-topbar.vue'))['default'];
        BaseTree: (typeof import('./base-tree.vue'))['default'];
        CardRectEditor: (typeof import('./card-rect-editor.vue'))['default'];
        CollapseItemWrapper: (typeof import('./collapse-item-wrapper.vue'))['default'];
        CollapseItemWrapperHeader: (typeof import('./collapse-item-wrapper-header.vue'))['default'];
        CollapseWrapper: (typeof import('./collapse-wrapper.vue'))['default'];
        DimensionEditor: (typeof import('./dimension-editor.vue'))['default'];
        DimensionSelector: (typeof import('./dimension-selector.vue'))['default'];
        DynamicImage: (typeof import('./dynamic-image.vue'))['default'];
        FileUploader: (typeof import('./file-uploader.vue'))['default'];
        FloatingPanel: (typeof import('./floating-panel.vue'))['default'];
        IconButton: (typeof import('./icon-button.vue'))['default'];
        IconTextButton: (typeof import('./icon-text-button.vue'))['default'];
        ImageCard: (typeof import('./image-card.vue'))['default'];
        ImageEditor: (typeof import('./image-editor.vue'))['default'];
        ImageErrorFallback: (typeof import('./image-error-fallback.vue'))['default'];
        ImageUploader: (typeof import('./image-uploader.vue'))['default'];
        Minimap: (typeof import('./minimap.vue'))['default'];
        Pagination: (typeof import('./pagination.vue'))['default'];
        RadioInput: (typeof import('./radio-input.vue'))['default'];
        RectEditor: (typeof import('./rect-editor.vue'))['default'];
        SearchScroll: (typeof import('./search-scroll.vue'))['default'];
        SearchScrollPage: (typeof import('./search-scroll-page.vue'))['default'];
        SelectBastags: (typeof import('./select-bastags.vue'))['default'];
        SelectPage: (typeof import('./select-page.vue'))['default'];
        SelectTag: (typeof import('./select-tag.vue'))['default'];
        SiteSelector: (typeof import('./site-selector.vue'))['default'];
        SpaceEditor: (typeof import('./space-editor.vue'))['default'];
        StatusDot: (typeof import('./status-dot.vue'))['default'];
        TabGroup: (typeof import('./tab-group.vue'))['default'];
        TagsGroup: (typeof import('./tags-group.vue'))['default'];
        TagsSelector: (typeof import('./tags-selector.vue'))['default'];
        TextMarquee: (typeof import('./text-marquee.vue'))['default'];
        ThreePanelLayout: (typeof import('./three-panel-layout.vue'))['default'];
        UploadComponentPackage: (typeof import('./upload-component-package.vue'))['default'];
    }
}
