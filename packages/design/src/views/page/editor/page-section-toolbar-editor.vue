<template>
    <div class="relative w-full h-[40px] flex items-end justify-between z-[99]">
        <div v-if="showTitle" class="w-full truncate text-2xl">
            <el-tooltip :content="title" teleported placement="top">
                {{ title }}
            </el-tooltip>
        </div>
        <div
            v-else
            class="flex-1 h-full flex items-center text-xs text-gray-300 hover:text-gray-500 transition-colors cursor-pointer"
            @click.stop="pageDesignerStore.switchElement('Page', pageDesignerStore.page.code)"></div>

        <div class="flex justify-end gap-1">
            <el-button
                :disabled="
                    !canEdit(pageDesignerStore.page) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.CREATE, {
                        type: 'org',
                        value: pageSection.orgId,
                    })
                "
                v-if="pageDesignerStore.mode === 'page'"
                icon="Plus"
                type="primary"
                size="default"
                @click="$emit('add', pageSection.code)">
                新增楼层
            </el-button>
            <el-button
                :disabled="
                    !canEdit(pageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.CHANGE, {
                        type: 'org',
                        value: pageSection.orgId,
                    })
                "
                icon="Switch"
                type="warning"
                size="default"
                @click="$emit('change', pageSection.code)">
                更换楼层
            </el-button>
            <el-button
                :disabled="
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.SAVE_AS_SECTION, {
                        type: 'org',
                        value: pageSection.orgId,
                    })
                "
                v-if="pageSection.layout.mode === DesignMode.SCROLLABLE"
                icon="Share"
                type="success"
                size="default"
                @click="$emit('save-as-section', pageSection.code)">
                另存为楼层定义
            </el-button>
            <el-button
                :disabled="
                    !canAudit(pageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.AUDIT, {
                        type: 'org',
                        value: pageSection.orgId,
                    })
                "
                type="warning"
                size="default"
                @click="$emit('publish', pageSection.code)">
                <i-mdi-share class="mr-2 w-4 h-4" />
                送审楼层
            </el-button>
            <el-button
                :disabled="
                    !canAudit(pageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.AUDIT_ALL, {
                        type: 'org',
                        value: pageSection.orgId,
                    })
                "
                type="warning"
                size="default"
                @click="$emit('publish-all', pageSection.code)">
                <i-mdi-share-all class="mr-2 w-4 h-4" />
                全部送审
            </el-button>
            <el-button
                :disabled="
                    !canEdit(pageDesignerStore.page) ||
                    !canDelete(pageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.DELETE, {
                        type: 'org',
                        value: pageSection.orgId,
                    })
                "
                icon="Delete"
                type="danger"
                size="default"
                @click.stop="$emit('delete', pageSection.code)">
                删除楼层
            </el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed } from 'vue';
    import { DesignMode, PageSection } from '@smartdesk/common/types';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { canAudit, canDelete, canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';

    // 页面楼层工具条编辑器
    defineOptions({
        name: 'PageSectionToolbarEditor',
    });

    // 参数
    const props = defineProps<{
        pageSection: PageSection;
    }>();

    // 事件
    defineEmits<{
        (e: 'add', code: string): void;
        (e: 'change', code: string): void;
        (e: 'save-as-section', code: string): void;
        (e: 'publish', code: string): void;
        (e: 'publish-all', code: string): void;
        (e: 'delete', code: string): void;
    }>();

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const permissionStore = usePermissionStore();

    // 是否显示页面楼层标题的响应式引用
    const showTitle = computed(() => {
        return props.pageSection.layout?.props?.showTitle ?? false;
    });

    // 页面楼层标题的响应式引用
    const title = computed(() => {
        return props.pageSection.layout?.props?.title ?? '';
    });
</script>
