<template>
    <div class="absolute w-full h-full" style="pointer-events: none">
        <div class="h-full w-full">
            <component :is="'epgui-' + cell.componentType" v-bind="cellProps" :key="componentKey"></component>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref, watchEffect } from 'vue';
    import { PageCell, PageSection } from '@smartdesk/common/types';
    import { getDataConvert } from '@epgui/epg-components';
    import { usePageDesignerStore } from '@smartdesk/design/stores';

    // 页面设计器坑位画布编辑器
    defineOptions({
        name: 'PageSectionCellCanvasEditor',
    });

    // 参数
    const props = defineProps<{
        cell: PageCell;
        pageSection: PageSection;
    }>();

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 缓存数据转换函数
    const dataConverter = computed(() => getDataConvert(props.cell.componentType));

    // 缓存坑位数据
    const cellItemData = computed(() => pageDesignerStore.convertToCellItemData(props.pageSection, props.cell));

    // 传入动态组件的属性
    const cellProps = computed(() => {
        return {
            ...props.cell.layout.props,
            rect: {
                top: 0,
                left: 0,
                width: props.cell.layout.rect.width,
                height: props.cell.layout.rect.height,
            },
            data: dataConverter.value(cellItemData.value),
        };
    });

    // 使用时间戳作为刷新标识，避免复杂的哈希计算
    const refreshTimestamp = ref(Date.now());

    // 简化的组件 key
    const componentKey = computed(() => {
        return `${props.cell.code}-${props.cell.componentType}-${refreshTimestamp.value}`;
    });

    // 监听关键数据变化，触发组件刷新
    watchEffect(() => {
        // 监听这些变化会触发组件重新渲染
        const _ = [
            props.cell.layout.rect.width,
            props.cell.layout.rect.height,
            props.cell.componentType,
            JSON.stringify(props.cell.layout.props),
            cellItemData.value,
            pageDesignerStore.selectedCellItemMap.get(props.cell.code),
            pageDesignerStore.getComponentVersion(props.cell.code),
        ];

        // 更新时间戳，触发组件刷新
        refreshTimestamp.value = Date.now();
    });
</script>
