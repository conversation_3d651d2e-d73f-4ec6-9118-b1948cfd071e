<template>
    <div class="flex flex-col h-full p-2">
        <el-tabs v-model="pageDesignerStore.leftPanelActiveTab">
            <el-tab-pane label="楼层定义" name="section">
                <section-list-selector v-if="pageDesignerStore.leftPanelActiveTab === 'section'" />
            </el-tab-pane>
            <el-tab-pane label="组件样式" name="componentStyle">
                <component-list-selector v-if="pageDesignerStore.leftPanelActiveTab === 'componentStyle'" />
            </el-tab-pane>
            <el-tab-pane
                label="桌面导航"
                name="nav"
                v-if="pageDesignerStore.desktopCode && pageDesignerStore.mode === 'page'">
                <nav-list-selector v-if="pageDesignerStore.leftPanelActiveTab === 'nav'" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup lang="ts">
    import SectionListSelector from '../selector/section-list-selector.vue';
    import ComponentListSelector from '@smartdesk/design/views/section/selector/component-list-selector.vue';
    import NavListSelector from '../selector/nav-list-selector.vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';

    // 页面设计器左侧面板
    defineOptions({
        name: 'PageLeftPanel',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
</script>
