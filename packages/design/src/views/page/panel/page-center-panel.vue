<template>
    <base-canvas ref="baseCanvasRef" :content-width="pageDesignerStore.page.layout.rect.width">
        <template #navigation>
            <page-section-navigation-editor
                class="absolute top-[70px] right-[0px] z-[22]"
                @navigate="pageDesignerStore.scrollToPageSection" />
        </template>

        <template #overlay>
            <page-canvas-editor />
        </template>
    </base-canvas>
</template>

<script setup lang="ts">
    import { onMounted, ref } from 'vue';
    import PageCanvasEditor from '../editor/page-canvas-editor.vue';
    import PageSectionNavigationEditor from '../editor/page-section-navigation-editor.vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';

    // 页面设计器中心面板
    defineOptions({
        name: 'PageCenterPanel',
    });

    // 基础画布引用
    const baseCanvasRef = ref<HTMLCanvasElement | null>(null);

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 组件挂载后注册画布引用到 store
    onMounted(() => {
        if (baseCanvasRef.value) {
            pageDesignerStore.registerBaseCanvas(baseCanvasRef.value);
        }
    });
</script>
