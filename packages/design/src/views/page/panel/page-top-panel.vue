<template>
    <base-top-bar
        :title="title"
        :show-save="false"
        :can-publish="canAudit(pageDesignerStore.page) && hasPublishPermission"
        :can-publish-all="canAudit(pageDesignerStore.page) && hasPublishAllPermission"
        @preview="handlePreview"
        @publish="handlePublishPageClick"
        @publish-all="handlePublishPageAllClick">
        <template #center>
            <div class="flex items-center gap-2">
                <el-switch
                    v-model="pageDesignerStore.showDeletedPageSections"
                    :active-text="`显示预删除 (${deletedCount})`"
                    inactive-text="隐藏预删除"
                    class="mx-4" />
            </div>
        </template>
    </base-top-bar>
</template>

<script setup lang="ts">
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { publishApi } from '@smartdesk/common/api';
    import { computed } from 'vue';
    import { useFeedback } from '@smartdesk/common/composables';
    import { canAudit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';

    // 页面设计器顶部面板
    defineOptions({
        name: 'PageTopPanel',
    });

    // 参数
    defineProps({
        title: {
            type: String,
            required: true,
        },
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const feedback = useFeedback();
    const permissionStore = usePermissionStore();

    // 删除计数
    const deletedCount = computed(() => {
        return pageDesignerStore.page.pageSectionList.filter((pageSection) => pageSection.delFlag === -1).length;
    });

    // 能否发布页面/桌面
    const hasPublishPermission = computed<boolean>(() => {
        // 校验是否有桌面或页面编辑权限
        if (pageDesignerStore.mode === 'page') {
            // 当前为页面，如果没有页面操作权限，不可拖拽
            if (
                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE.AUDIT, {
                    type: 'org',
                    value: pageDesignerStore.page.orgId,
                })
            ) {
                return false;
            }
        } else if (pageDesignerStore.mode === 'desktop') {
            // 当前为桌面，如果没有桌面操作权限，不可拖拽
            if (
                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.DESKTOP.AUDIT, {
                    type: 'org',
                    value: pageDesignerStore.page.orgId,
                })
            ) {
                return false;
            }
        }
        return true;
    });

    // 能否全部发布页面/桌面
    const hasPublishAllPermission = computed<boolean>(() => {
        // 校验是否有桌面或页面编辑权限
        if (pageDesignerStore.mode === 'page') {
            // 当前为页面，如果没有页面操作权限，不可拖拽
            if (
                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE.AUDIT_ALL, {
                    type: 'org',
                    value: pageDesignerStore.page.orgId,
                })
            ) {
                return false;
            }
        } else if (pageDesignerStore.mode === 'desktop') {
            // 当前为桌面，如果没有桌面操作权限，不可拖拽
            if (
                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.DESKTOP.AUDIT_ALL, {
                    type: 'org',
                    value: pageDesignerStore.page.orgId,
                })
            ) {
                return false;
            }
        }
        return true;
    });

    // 处理送审桌面/页面
    const handlePublishPageClick = async () => {
        if (pageDesignerStore.mode === 'page') {
            const res = await publishApi.publishSelf('Page', pageDesignerStore.page.code, 'CREATE');
            if (res && res.code === 200) {
                feedback.success('送审页面成功');

                // 刷新页面以更新UI
                await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
            } else {
                feedback.error('送审页面失败：' + res.msg);
            }
        } else if (pageDesignerStore.mode === 'desktop') {
            const res = await publishApi.publishSelf('Desktop', pageDesignerStore.page.code, 'CREATE');
            if (res && res.code === 200) {
                feedback.success('送审桌面成功');

                // 刷新页面以更新UI
                await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
            } else {
                feedback.error('送审桌面失败：' + res.msg);
            }
        }
    };

    // 处理全部送审桌面/页面及其关联实体
    const handlePublishPageAllClick = async () => {
        let res: any;
        if (pageDesignerStore.mode === 'page') {
            res = await publishApi.publishComplete('Page', pageDesignerStore.page.code, 'CREATE');
        } else if (pageDesignerStore.mode === 'desktop') {
            res = await publishApi.publishComplete('Desktop', pageDesignerStore.page.code, 'CREATE');
        }
        if (res && res.code === 200) {
            feedback.success('全部送审成功');

            // 刷新页面以更新UI
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
        } else {
            feedback.error('全部送审失败：' + res.msg);

            // 从 msg 中提取
            const matches = [...res.msg.matchAll(/\[(.*?)\]/g)];
            const codes = matches ? matches.map((match) => match[1]) : [];
            if (codes && codes.length > 1) {
                pageDesignerStore.scrollToPageSection(codes[0]);
                pageDesignerStore.switchElement('Cell', codes[1]);
            }
        }
    };

    // 处理预览
    const handlePreview = () => {
        const pageCode = pageDesignerStore.page.code;

        // 当前地址
        const base = window.location.origin;

        // 拼接 URL 为你需要的格式
        const fullUrl = `${base}/preview?pageCode=${pageCode}`;

        // 打开新窗口预览
        window.open(fullUrl, '_blank');
    };
</script>
