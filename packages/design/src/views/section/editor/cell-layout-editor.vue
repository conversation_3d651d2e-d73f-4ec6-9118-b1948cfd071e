<template>
    <el-form v-if="sectionDesignerStore.currentCell" label-width="80px" label-suffix=":">
        <collapse-wrapper v-model="activeNames">
            <collapse-item-wrapper name="baseInfo" title="基本信息">
                <el-form-item label="坑位名称">
                    <el-input
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="sectionDesignerStore.currentCell.name"
                        @change="handleCellRectChange"
                        size="default" />
                </el-form-item>
            </collapse-item-wrapper>

            <collapse-item-wrapper name="cellLayout" title="坑位尺寸">
                <el-form-item label="上">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="cellRect.top"
                        @change="handleCellRectChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="左">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="cellRect.left"
                        @change="handleCellRectChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="宽">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="cellRect.width"
                        @change="handleCellRectChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="高">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="cellRect.height"
                        @change="handleCellRectChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
            </collapse-item-wrapper>
        </collapse-wrapper>
    </el-form>

    <div v-else class="text-center text-gray-400">
        <el-empty description="请选择任意坑位" :image-size="60" />
    </div>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';

    // 坑位布局编辑器
    defineOptions({
        name: 'CellLayoutEditor',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const permissionStore = usePermissionStore();

    // 展示
    const activeNames = ref<string[]>(['baseInfo', 'cellLayout']);

    // 创建坑位尺寸的响应式引用
    const cellRect = computed(() => {
        if (sectionDesignerStore.currentCell) {
            return sectionDesignerStore.currentCell.layout.rect;
        }
        return { top: 0, left: 0, width: 0, height: 0 };
    });

    // 处理坑位尺寸变化
    const handleCellRectChange = () => {
        sectionDesignerStore.saveHistory();
    };
</script>
