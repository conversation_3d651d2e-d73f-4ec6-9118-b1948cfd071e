<template>
    <div
        style="box-sizing: border-box"
        class="transition-all relative w-fit h-full overflow-auto"
        @dragenter="onLayoutDragEnter"
        @dragover="onLayoutDragOver"
        @dragleave="onLayoutDragLeave"
        @drop="onLayoutDrop">
        <div
            ref="sectionCanvasRef"
            class="relative transition-all"
            :class="sectionClass"
            :style="sectionStyle"
            @click.self="selectSection"
            @wheel.prevent="handleWheel">
            <div class="absolute" @click.stop="selectSection">
                <div
                    v-for="(cell, index) in sectionDesignerStore.sectionLayout.cells"
                    :key="cell.code"
                    class="absolute transition-all duration-200 group"
                    :class="cellClass(cell, index)"
                    :style="cellStyle(cell)"
                    @click.stop="onClickCell(cell, index)"
                    @dragenter.prevent="(e) => onCellDragEnter(e, index)"
                    @dragover.prevent="() => {}"
                    @drop.prevent="(e) => onCellDrop(e, index)"
                    @dragleave.prevent="(e) => onCellDragLeave(e, index)">
                    <div class="w-full h-full overflow-hidden flex items-center justify-center relative">
                        <div
                            v-if="index === sectionDesignerStore.currentCellIndex && !sectionDesignerStore.batchMode"
                            class="absolute inset-0 bg-blue-500/20 animate-pulse z-10 pointer-events-none"></div>
                        <div
                            class="absolute top-0 right-0 h-7 w-full bg-gray-200/80 text-black text-right text-base cursor-pointer z-10">
                            <div class="whitespace-nowrap overflow-auto scrollbar-hide h-full mr-1">
                                {{ ratio(cell) }}
                            </div>
                        </div>
                        <el-image
                            v-if="getCellIcon(cell.componentStyleCode)"
                            :src="getCellIcon(cell.componentStyleCode)"
                            class="z-0">
                            <template #error>
                                <image-error-fallback text="图标损坏" />
                            </template>
                        </el-image>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="isScrollableMode && showScrollButtons" class="absolute inset-0 pointer-events-none">
            <el-button
                v-if="canScrollLeft"
                class="absolute left-2 top-1/2 transform -translate-y-1/2 pointer-events-auto z-[60] shadow-lg"
                type="primary"
                circle
                size="small"
                @click="scrollLeft">
                <template #icon>
                    <el-icon><ArrowLeft /></el-icon>
                </template>
            </el-button>

            <el-button
                v-if="canScrollRight"
                class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-auto z-[60] shadow-lg"
                type="primary"
                circle
                size="small"
                @click="scrollRight">
                <template #icon>
                    <el-icon><ArrowRight /></el-icon>
                </template>
            </el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, type CSSProperties, ref, watch, onMounted, onUnmounted } from 'vue';
    import { useComponentListStore, useSectionDesignerStore } from '@smartdesk/design/stores';
    import { useCanvasDownload, useFeedback } from '@smartdesk/common/composables';
    import { storageApi } from '@smartdesk/common/api';
    import { CellLayoutFile, ComponentStyle, DesignMode, Layout } from '@smartdesk/common/types';
    import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

    // 画布编辑器
    defineOptions({
        name: 'SectionCanvasEditor',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const componentListStore = useComponentListStore();
    const { confirm } = useFeedback();

    // 截图
    const sectionCanvasRef = ref<HTMLElement | null>(null);
    const { uploadToServer } = useCanvasDownload(
        sectionCanvasRef,
        {
            format: 'png',
            backgroundColor: '#ffffff',
        },
        storageApi.uploadFile
    );

    // 是否是滚动模式
    const isScrollableMode = computed<boolean>(
        () => sectionDesignerStore.sectionLayout.layout.mode === DesignMode.SCROLLABLE
    );

    // 滚动相关状态
    const canScrollLeft = ref<boolean>(false);
    const canScrollRight = ref<boolean>(false);
    const showScrollButtons = ref<boolean>(false);

    // 尺寸信息显示状态
    const showDimensions = ref<boolean>(true);

    // 坑位宽高比
    const ratio = (cell: CellLayoutFile) => {
        if (!cell.layout.rect.width || !cell.layout.rect.height) return '';
        return Math.trunc(cell.layout.rect.width) + ' / ' + Math.trunc(cell.layout.rect.height);
    };

    // 楼层定义样式
    const sectionStyle = computed<CSSProperties>(() => ({
        top: `${sectionDesignerStore.sectionLayout.layout.rect.top}px`,
        left: `${sectionDesignerStore.sectionLayout.layout.rect.left}px`,
        width: `${sectionDesignerStore.sectionLayout.layout.rect.width}px`,
        height: `${sectionDesignerStore.sectionLayout.layout.rect.height}px`,
    }));

    // 楼层定义类
    const sectionClass = computed(() => {
        /**
         * 楼层定义的几种状态
         * 1、未选中：border-2 border-gray-300
         * 2、选中：border-2 border-blue-500
         * 3、滚动模式：overflow-x-auto overflow-y-hidden whitespace-nowrap
         * 4、接受布局拖拽：border-2 border-green-500
         * */

        return {
            'border-2 border-gray-300': sectionDesignerStore.currentCellIndex !== -1,
            'border-2 border-blue-500': sectionDesignerStore.currentCellIndex === -1,
            'overflow-x-auto overflow-y-hidden whitespace-nowrap scrollbar-hide': isScrollableMode.value,
            'border-2 border-green-500': dragType.value === 'layout',
        };
    });

    // 坑位样式
    const cellStyle = (cell: CellLayoutFile): CSSProperties => ({
        left: `${cell.layout.rect.left}px`,
        top: `${cell.layout.rect.top}px`,
        width: `${cell.layout.rect.width}px`,
        height: `${cell.layout.rect.height}px`,
    });

    // 坑位类
    const cellClass = (cell: CellLayoutFile, index: number) => {
        /**
         * 坑位的几种状态
         * 1、非批量模式下，没有被选中：border-2 border-gray-300
         * 2、非批量模式下，被选中：border-2 border-blue-500
         * 3、批量模式下，可被选择：border-2 border-gray-300 cursor-pointer
         * 4、批量模式下，不可被选择：border-2 border-yellow-300/50 cursor-not-allowed
         * 5、批量模式下，已被选择：border-2 border-blue-500 cursor-pointer
         * 6、接受组件样式拖拽：border-2 border-green-500
         * 7、滚动模式下，absolute transform translate-x-0 translate-y-0 translate-z-0
         * */

        return {
            'border-2 border-gray-300':
                !sectionDesignerStore.batchMode && sectionDesignerStore.currentCellIndex !== index,
            'border-2 border-blue-500':
                !sectionDesignerStore.batchMode && sectionDesignerStore.currentCellIndex === index,
            'border-2 border-gray-300 cursor-pointer': sectionDesignerStore.batchMode && !isCellSelected(cell),
            'border-2 border-yellow-300/50 cursor-not-allowed':
                sectionDesignerStore.batchMode && !canCellSelected(cell),
            'border-2 border-blue-500 cursor-pointer': sectionDesignerStore.batchMode && isCellSelected(cell),
            'border-2 border-green-500': cellDragOverIndex.value === index && dragType.value === 'componentStyle',
            'absolute transform translate-x-0 translate-y-0 translate-z-0': isScrollableMode.value,
        };
    };

    // 判断坑位是否已被选择
    const isCellSelected = (cell: CellLayoutFile) => {
        return sectionDesignerStore.batchList.map((item) => item.code).includes(cell.code);
    };

    // 判断坑位是否可被选择
    const canCellSelected = (cell: CellLayoutFile) => {
        // 默认都是可以被选择的
        return true;
    };

    // 获取坑位对应组件的缩略图
    const getCellIcon = (componentStyleCode: string) => {
        return componentListStore.state.componentStyleMap.get(componentStyleCode)?.icon;
    };

    // 选择楼层
    const selectSection = () => {
        sectionDesignerStore.selectCell(-1);
    };

    // 点击坑位
    const onClickCell = (cellLayoutFile: CellLayoutFile, index: number) => {
        // 批量模式下，且被点击的坑位是可被点击的，走批量模式的选择
        if (sectionDesignerStore.batchMode) {
            if (sectionDesignerStore.batchType === 'copy') {
                // 复制模式：处理整行选中和取消
                handleRowSelection(cellLayoutFile);
            } else if (canCellSelected(cellLayoutFile)) {
                sectionDesignerStore.handleSelectBatch(cellLayoutFile);
            }
            return;
        }

        // 正常模式下，直接选择坑位
        sectionDesignerStore.selectCell(index);
    };

    // 处理行选中逻辑
    const handleRowSelection = (cellLayoutFile: CellLayoutFile) => {
        // 找到点击坑位所在的行
        const clickedRow = sectionDesignerStore.cellRows.find((row) =>
            row.cells.some((cell: any) => cell.code === cellLayoutFile.code)
        );

        if (!clickedRow) return;

        // 检查当前行是否已经被选中
        const isRowSelected = clickedRow.cells.every((cell: any) =>
            sectionDesignerStore.batchList.some((batchCell) => batchCell.code === cell.code)
        );

        if (isRowSelected) {
            // 如果整行已选中，则取消选中整行
            sectionDesignerStore.batchList = sectionDesignerStore.batchList.filter(
                (batchCell) => !clickedRow.cells.some((cell: any) => cell.code === batchCell.code)
            );
        } else {
            // 如果整行未选中，则选中整行
            // 先移除该行中已选中的坑位
            sectionDesignerStore.batchList = sectionDesignerStore.batchList.filter(
                (batchCell) => !clickedRow.cells.some((cell: any) => cell.code === batchCell.code)
            );
            // 再添加整行坑位
            sectionDesignerStore.batchList.push(...clickedRow.cells);
        }
    };

    // 拖拽类型
    const dragType = ref<'layout' | 'componentStyle' | null>(null);
    const cellDragOverIndex = ref<number | null>(null);

    // 是否是布局拖拽
    const isLayoutDrag = (event: DragEvent) => {
        const types = event.dataTransfer?.types || [];
        return types.some((type) => type.toLowerCase().includes('layout'));
    };

    // 是否是组件样式拖拽
    const isComponentStyleDrag = (event: DragEvent) => {
        const types = event.dataTransfer?.types || [];
        return types.some((type) => type.toLowerCase().includes('componentstyle'));
    };

    // 楼层布局拖拽进入
    const onLayoutDragEnter = (event: DragEvent) => {
        if (isLayoutDrag(event)) {
            dragType.value = 'layout';
        }
    };

    // 楼层布局拖拽结束
    const onLayoutDragOver = (event: DragEvent) => {
        event.preventDefault();
    };

    // 楼层布局拖拽离开
    const onLayoutDragLeave = () => {
        dragType.value = null;
        cellDragOverIndex.value = null;
    };

    // 楼层布局拖拽放下
    const onLayoutDrop = async (event: DragEvent) => {
        // 批量模式下，直接返回
        if (sectionDesignerStore.batchMode) {
            return;
        }

        if (dragType.value === 'layout') {
            const layoutStr = event.dataTransfer?.getData('layout');
            if (!layoutStr) {
                return;
            }
            const layout: Layout = JSON.parse(layoutStr);
            const res = await confirm('确定修改当前楼层布局为 ' + layout.name + ' 吗？');
            if (res && sectionDesignerStore.section) {
                // 更新布局和坑位信息
                sectionDesignerStore.section.layoutId = layout.id;
                sectionDesignerStore.section.layoutCode = layout.code;
                sectionDesignerStore.section.layout.layout = layout.layout.layout;
                sectionDesignerStore.section.layout.cells = layout.layout.cells;
            }
        }
        dragType.value = null;
        cellDragOverIndex.value = null;
    };

    // 坑位拖拽进入
    const onCellDragEnter = (event: DragEvent, index: number) => {
        // 批量模式下，直接返回
        if (sectionDesignerStore.batchMode) {
            return;
        }

        // 非组件样式拖拽下，直接返回
        if (!isComponentStyleDrag(event)) {
            return;
        }

        dragType.value = 'componentStyle';
        cellDragOverIndex.value = index;
        sectionDesignerStore.selectCell(index);
    };

    // 坑位拖拽离开
    const onCellDragLeave = (event: DragEvent, index: number) => {
        // 批量模式下，直接返回
        if (sectionDesignerStore.batchMode) {
            return;
        }

        // 非组件样式拖拽下，直接返回
        if (!isComponentStyleDrag(event)) {
            return;
        }

        if (cellDragOverIndex.value === index) {
            cellDragOverIndex.value = null;
        }
    };

    // 坑位拖拽放下
    const onCellDrop = (event: DragEvent, index: number) => {
        // 批量模式下，直接返回
        if (sectionDesignerStore.batchMode) {
            return;
        }

        // 非组件样式拖拽下，直接返回
        if (!isComponentStyleDrag(event)) {
            return;
        }

        // 处理接收组件样式数据
        const componentStyleStr = event.dataTransfer?.getData('componentStyle');
        if (!componentStyleStr) {
            return;
        }

        // 处理坑位更换组件样式
        const componentStyle: ComponentStyle = JSON.parse(componentStyleStr);
        sectionDesignerStore.sectionLayout.cells[index].component = componentStyle.type;
        sectionDesignerStore.sectionLayout.cells[index].layout.props = componentStyle.layout;

        // 处理完毕
        cellDragOverIndex.value = null;
        dragType.value = null;
    };

    // 处理滚轮事件
    const handleWheel = (e: WheelEvent) => {
        if (!isScrollableMode.value || !sectionCanvasRef.value) {
            return;
        }

        // 如果按下 Shift 键，将垂直滚动转换为水平滚动
        const deltaX = e.shiftKey ? e.deltaY : e.deltaX;
        sectionCanvasRef.value.scrollLeft += deltaX;

        // 更新滚动按钮状态
        updateScrollButtonsState();
    };

    // 更新滚动按钮状态
    const updateScrollButtonsState = () => {
        if (!isScrollableMode.value || !sectionCanvasRef.value) {
            canScrollLeft.value = false;
            canScrollRight.value = false;
            showScrollButtons.value = false;
            return;
        }

        const container = sectionCanvasRef.value;
        const scrollLeft = container.scrollLeft;
        const scrollWidth = container.scrollWidth;
        const clientWidth = container.clientWidth;

        // 检查是否需要显示滚动按钮
        showScrollButtons.value = scrollWidth > clientWidth;

        // 检查是否可以向左滚动
        canScrollLeft.value = scrollLeft > 0;

        // 检查是否可以向右滚动
        canScrollRight.value = scrollLeft < scrollWidth - clientWidth - 1;
    };

    // 向左滚动
    const scrollLeft = () => {
        if (!sectionCanvasRef.value) return;

        const scrollAmount = 200; // 每次滚动的距离
        sectionCanvasRef.value.scrollBy({
            left: -scrollAmount,
            behavior: 'smooth',
        });
    };

    // 向右滚动
    const scrollRight = () => {
        if (!sectionCanvasRef.value) return;

        const scrollAmount = 200; // 每次滚动的距离
        sectionCanvasRef.value.scrollBy({
            left: scrollAmount,
            behavior: 'smooth',
        });
    };

    // 滚动到指定坑位，支持对齐方式
    const scrollToCell = (cellIndex: number, align: 'center' | 'left' | 'right' = 'center') => {
        if (
            !isScrollableMode.value ||
            !sectionCanvasRef.value ||
            cellIndex < 0 ||
            cellIndex >= sectionDesignerStore.sectionLayout.cells.length
        )
            return;
        const cell = sectionDesignerStore.sectionLayout.cells[cellIndex];
        if (!cell) return;
        const container = sectionCanvasRef.value;
        const x = cell.layout.rect.left;
        const cellWidth = cell.layout.rect.width;
        const visibleWidth = container.clientWidth;
        let targetScrollLeft = 0;
        if (align === 'center') {
            targetScrollLeft = x + cellWidth / 2 - visibleWidth / 2;
        } else if (align === 'left') {
            targetScrollLeft = x;
        } else if (align === 'right') {
            targetScrollLeft = x + cellWidth - visibleWidth;
        }
        container.scrollLeft = Math.max(0, targetScrollLeft);
    };

    watch(
        () => sectionDesignerStore.currentCellIndex,
        (newVal) => {
            if (newVal !== -1) {
                scrollToCell(newVal, 'center');
            }
        }
    );

    // 滚动事件监听器
    const handleScroll = () => {
        updateScrollButtonsState();
    };

    // 组件挂载时初始化
    onMounted(() => {
        if (sectionCanvasRef.value) {
            sectionCanvasRef.value.addEventListener('scroll', handleScroll);
            // 初始化滚动按钮状态
            setTimeout(updateScrollButtonsState, 100);
        }
    });

    // 组件卸载时清理事件监听器
    onUnmounted(() => {
        if (sectionCanvasRef.value) {
            sectionCanvasRef.value.removeEventListener('scroll', handleScroll);
        }
    });

    // 获取右侧间距信息
    const getRightGapInfo = (cell: CellLayoutFile, index: number) => {
        const cells = sectionDesignerStore.sectionLayout.cells;
        const gapTolerance = (sectionDesignerStore.sectionLayout.layout.gap || 0) / 2;

        // 找到同一行右侧最近的坑位
        const rightCell = cells
            .filter(
                (c) =>
                    Math.abs(c.layout.rect.top - cell.layout.rect.top) <= gapTolerance &&
                    c.layout.rect.left > cell.layout.rect.left
            )
            .sort((a, b) => a.layout.rect.left - b.layout.rect.left)[0];

        if (rightCell) {
            const gap = rightCell.layout.rect.left - (cell.layout.rect.left + cell.layout.rect.width);
            return gap > 0 ? `${Math.round(gap)}px` : null;
        }
        return null;
    };

    // 获取下方间距信息
    const getBottomGapInfo = (cell: CellLayoutFile, index: number) => {
        const cells = sectionDesignerStore.sectionLayout.cells;
        const gapTolerance = (sectionDesignerStore.sectionLayout.layout.gap || 0) / 2;

        // 找到同一列下方最近的坑位
        const bottomCell = cells
            .filter(
                (c) =>
                    Math.abs(c.layout.rect.left - cell.layout.rect.left) <= gapTolerance &&
                    c.layout.rect.top > cell.layout.rect.top
            )
            .sort((a, b) => a.layout.rect.top - b.layout.rect.top)[0];

        if (bottomCell) {
            const gap = bottomCell.layout.rect.top - (cell.layout.rect.top + cell.layout.rect.height);
            return gap > 0 ? `${Math.round(gap)}px` : null;
        }
        return null;
    };

    // 获取左侧内边距信息
    const getLeftPaddingInfo = (cell: CellLayoutFile) => {
        const padding = sectionDesignerStore.sectionLayout.layout.padding;
        const tolerance = 5; // 5px容差

        if (padding && Math.abs(cell.layout.rect.left - (padding.left || 0)) <= tolerance) {
            return `${Math.round(padding.left || 0)}px`;
        }
        return null;
    };

    // 获取上方内边距信息
    const getTopPaddingInfo = (cell: CellLayoutFile) => {
        const padding = sectionDesignerStore.sectionLayout.layout.padding;
        const tolerance = 5; // 5px容差

        if (padding && Math.abs(cell.layout.rect.top - (padding.top || 0)) <= tolerance) {
            return `${Math.round(padding.top || 0)}px`;
        }
        return null;
    };

    // 获取右侧内边距信息
    const getRightPaddingInfo = (cell: CellLayoutFile) => {
        const padding = sectionDesignerStore.sectionLayout.layout.padding;
        const sectionWidth = sectionDesignerStore.sectionLayout.layout.rect.width;
        const tolerance = 5; // 5px容差

        if (padding && sectionWidth) {
            const cellRightEdge = cell.layout.rect.left + cell.layout.rect.width;
            const sectionRightEdge = sectionWidth - (padding.right || 0);

            if (Math.abs(cellRightEdge - sectionRightEdge) <= tolerance) {
                return `${Math.round(padding.right || 0)}px`;
            }
        }
        return null;
    };

    // 获取下方内边距信息
    const getBottomPaddingInfo = (cell: CellLayoutFile) => {
        const padding = sectionDesignerStore.sectionLayout.layout.padding;
        const sectionHeight = sectionDesignerStore.sectionLayout.layout.rect.height;
        const tolerance = 5; // 5px容差

        if (padding && sectionHeight) {
            const cellBottomEdge = cell.layout.rect.top + cell.layout.rect.height;
            const sectionBottomEdge = sectionHeight - (padding.bottom || 0);

            if (Math.abs(cellBottomEdge - sectionBottomEdge) <= tolerance) {
                return `${Math.round(padding.bottom || 0)}px`;
            }
        }
        return null;
    };

    // 暴露方法给父组件
    defineExpose({
        uploadToServer,
        scrollToCell,
    });
</script>

<style scoped>
    /* 隐藏滚动条 */
    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
</style>
