<template>
    <el-form label-width="80px" label-suffix=":">
        <collapse-wrapper v-model="activeNames">
            <collapse-item-wrapper v-if="sectionDesignerStore.section" name="baseInfo" title="基本信息">
                <el-form-item label="名称">
                    <el-input
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="sectionDesignerStore.section.name"
                        @change="handleSectionChange"
                        size="default" />
                </el-form-item>
                <el-form-item label="分辨率">
                    <el-select
                        v-model="sectionDesignerStore.section.resolution"
                        placeholder="请选择分辨率"
                        size="default"
                        disabled>
                        <el-option
                            v-for="item in resolutionOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </collapse-item-wrapper>

            <collapse-item-wrapper name="layoutMode" title="模式切换">
                <el-form-item label="布局模式">
                    <el-radio-group
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="mode"
                        @change="handleModeChange">
                        <el-radio-button :value="DesignMode.FIXED">固定模式</el-radio-button>
                        <el-radio-button :value="DesignMode.SCROLLABLE">滚动模式</el-radio-button>
                    </el-radio-group>
                </el-form-item>
            </collapse-item-wrapper>

            <collapse-item-wrapper name="sectionSize" title="楼层尺寸">
                <el-form-item label="上">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="sectionDesignerStore.sectionLayout.layout.rect.top"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="左">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="sectionDesignerStore.sectionLayout.layout.rect.left"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="宽">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="sectionDesignerStore.sectionLayout.layout.rect.width"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="高">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="sectionDesignerStore.sectionLayout.layout.rect.height"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
            </collapse-item-wrapper>

            <collapse-item-wrapper name="sectionPadding" title="楼层内边距">
                <el-form-item label="上">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="layoutPadding.top"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="下">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="layoutPadding.bottom"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="左">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="layoutPadding.left"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="右">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="layoutPadding.right"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
            </collapse-item-wrapper>

            <collapse-item-wrapper name="sectionCell" title="楼层坑位">
                <el-form-item label="坑位间距">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="layoutGap"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
            </collapse-item-wrapper>

            <collapse-item-wrapper
                v-if="sectionDesignerStore.sectionLayout.layout.mode === DesignMode.SCROLLABLE"
                name="standardCell"
                title="标准坑位尺寸">
                <el-form-item label="宽">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="standardSize.width"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
                <el-form-item label="高">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="standardSize.height"
                        @change="handleSectionChange"
                        :min="0"
                        size="default"
                        :precision="0"
                        style="width: 100%">
                        <template #suffix>
                            <span>px</span>
                        </template>
                    </el-input-number>
                </el-form-item>
            </collapse-item-wrapper>
        </collapse-wrapper>
    </el-form>
</template>

<script setup lang="ts">
    import { DesignMode, Padding, StandardSize } from '@smartdesk/common/types';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { computed, ref } from 'vue';
    import { Enumeration, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';

    // 楼层布局编辑器
    defineOptions({
        name: 'SectionLayoutEditor',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();

    // 展示
    const activeNames = ref<string[]>([
        'baseInfo',
        'layoutMode',
        'sectionSize',
        'sectionPadding',
        'sectionCell',
        'standardCell',
    ]);

    // 分辨率枚举
    const resolutionOption = ref<Enumeration[]>(enumStore.getEnumsByKey('resolution') || []);

    // 楼层模式的响应式引用
    const mode = computed({
        get: () => {
            if (!sectionDesignerStore.section.layout.layout.mode) {
                // 默认固定模式
                sectionDesignerStore.section.layout.layout.mode = DesignMode.FIXED;
            }
            return sectionDesignerStore.section.layout.layout.mode;
        },
        set: (value: DesignMode) => {
            sectionDesignerStore.section.layout.layout.mode = value;
        },
    });

    // 楼层内边距的响应式引用
    const layoutPadding = computed({
        get: () => {
            // 确保 padding 对象存在
            if (!sectionDesignerStore.sectionLayout.layout.padding) {
                sectionDesignerStore.sectionLayout.layout.padding = {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0,
                };
            }
            return sectionDesignerStore.sectionLayout.layout.padding;
        },
        set: (value: Padding) => {
            sectionDesignerStore.sectionLayout.layout.padding = value;
        },
    });

    // 楼层间距的响应式引用
    const layoutGap = computed({
        get: () => {
            return sectionDesignerStore.sectionLayout.layout.gap || 0;
        },
        set: (value: number) => {
            sectionDesignerStore.sectionLayout.layout.gap = value;
        },
    });

    // 创建标准坑位尺寸的响应式引用
    const standardSize = computed({
        get: () => {
            if (!sectionDesignerStore.sectionLayout.layout.standardSize) {
                sectionDesignerStore.sectionLayout.layout.standardSize = {
                    width: 200,
                    height: 180,
                };
            }
            return sectionDesignerStore.sectionLayout.layout.standardSize;
        },
        set: (value: StandardSize) => {
            sectionDesignerStore.sectionLayout.layout.standardSize = value;
        },
    });

    // 处理模式切换
    const handleModeChange = (mode: DesignMode) => {
        sectionDesignerStore.switchDesignMode(mode);
        sectionDesignerStore.saveHistory();
    };

    // 处理楼层布局变化
    const handleSectionChange = () => {
        sectionDesignerStore.saveHistory();
    };
</script>
