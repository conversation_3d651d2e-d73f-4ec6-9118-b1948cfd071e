<template>
    <section-toolbar-editor
        class="py-3 border-b border-gray-200"
        @confirm-batch="handleConfirmBatch"
        @cancel-batch="handleCancelBatch" />

    <base-canvas
        canvas-height="calc(100% - 50px)"
        :content-width="sectionDesignerStore.sectionLayout.layout.rect.width">
        <template #navigation>
            <section-row-navigation-editor class="absolute top-[70px] right-[0px] z-[22]" />
        </template>

        <template #overlay>
            <section-canvas-editor ref="sectionCanvasEditorRef" />
        </template>
    </base-canvas>
</template>

<script setup lang="ts">
    import SectionToolbarEditor from '../editor/section-toolbar-editor.vue';
    import SectionCanvasEditor from '../editor/section-canvas-editor.vue';
    import SectionRowNavigationEditor from '../editor/section-row-navigation-editor.vue';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { ref } from 'vue';

    // 楼层定义设计器中心面板
    defineOptions({
        name: 'SectionCenterPanel',
    });

    // 事件
    const emit = defineEmits(['confirm-batch', 'cancel-batch']);

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();

    // ref
    const sectionCanvasEditorRef = ref<any>();

    // 确认批量操作
    const handleConfirmBatch = () => {
        emit('confirm-batch');
    };

    // 取消批量操作
    const handleCancelBatch = () => {
        emit('cancel-batch');
    };

    // 暴露方法给父组件
    defineExpose({
        sectionCanvasEditorRef,
    });
</script>
