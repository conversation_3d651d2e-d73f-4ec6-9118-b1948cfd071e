import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const usePanelLayoutStore = defineStore('panelLayout', () => {
    // 面板宽度
    const leftWidth = ref(240);
    const rightWidth = ref(240);
    const containerWidth = ref(0);

    // 拖拽状态
    const isResizing = ref(false);
    const currentHandle = ref<'left' | 'right' | null>(null);
    const startX = ref(0);
    const startLeftWidth = ref(0);
    const startRightWidth = ref(0);

    // 面板可见状态
    const leftVisible = ref(true);
    const rightVisible = ref(true);

    // 计算中间区域的宽度
    const centerWidth = computed(() => {
        const total = containerWidth.value;
        const left = leftVisible.value ? leftWidth.value : 0;
        const right = rightVisible.value ? rightWidth.value : 0;
        return Math.max(total - left - right, 300); // 默认最小宽度300
    });

    // 切换左侧面板
    function toggleLeftPanel() {
        leftVisible.value = !leftVisible.value;
    }

    // 切换右侧面板
    function toggleRightPanel() {
        rightVisible.value = !rightVisible.value;
    }

    // 开始调整大小
    function startResize(handle: 'left' | 'right', clientX: number) {
        isResizing.value = true;
        currentHandle.value = handle;
        startX.value = clientX;
        startLeftWidth.value = leftWidth.value;
        startRightWidth.value = rightWidth.value;
    }

    // 处理鼠标移动
    function handleResize(clientX: number, minLeftWidth = 100, minRightWidth = 100, minCenterWidth = 300) {
        if (!isResizing.value) return;

        const deltaX = clientX - startX.value;

        if (currentHandle.value === 'left') {
            // 调整左侧面板宽度
            const newLeftWidth = Math.max(minLeftWidth, startLeftWidth.value + deltaX);

            // 确保不会导致中间区域小于最小宽度
            const maxLeftWidth = containerWidth.value - (rightVisible.value ? rightWidth.value : 0) - minCenterWidth;

            leftWidth.value = Math.min(newLeftWidth, maxLeftWidth);
        } else if (currentHandle.value === 'right') {
            // 调整右侧面板宽度
            const newRightWidth = Math.max(minRightWidth, startRightWidth.value - deltaX);

            // 确保不会导致中间区域小于最小宽度
            const maxRightWidth = containerWidth.value - (leftVisible.value ? leftWidth.value : 0) - minCenterWidth;

            rightWidth.value = Math.min(newRightWidth, maxRightWidth);
        }
    }

    // 停止调整大小
    function stopResize() {
        isResizing.value = false;
        currentHandle.value = null;
    }

    // 更新容器宽度
    function updateContainerWidth(width: number) {
        containerWidth.value = width;
    }

    // 初始化面板配置
    function initPanelLayout(config: {
        leftWidth?: number;
        rightWidth?: number;
        leftVisible?: boolean;
        rightVisible?: boolean;
    }) {
        if (config.leftWidth) leftWidth.value = config.leftWidth;
        if (config.rightWidth) rightWidth.value = config.rightWidth;
        if (config.leftVisible !== undefined) leftVisible.value = config.leftVisible;
        if (config.rightVisible !== undefined) rightVisible.value = config.rightVisible;
    }

    return {
        // 状态
        leftWidth,
        rightWidth,
        centerWidth,
        containerWidth,
        isResizing,
        leftVisible,
        rightVisible,

        // 方法
        toggleLeftPanel,
        toggleRightPanel,
        startResize,
        handleResize,
        stopResize,
        updateContainerWidth,
        initPanelLayout,
    };
});
