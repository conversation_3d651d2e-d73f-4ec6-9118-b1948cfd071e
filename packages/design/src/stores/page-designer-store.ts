import { defineStore, StateTree } from 'pinia';
import { STORAGE_DRIVER } from '@chances/portal_common_core';
import {
    CellItemData,
    ComponentStyle,
    ContentData,
    DesignMode,
    LayoutFile,
    Page,
    PageCell,
    PageCellItem,
    PageSection,
    ScheduleRule,
    Section,
} from '@smartdesk/common/types';
import { computed, nextTick, reactive, ref, toRaw } from 'vue';
import { cellApi, cmsApi, pageApi, pageSectionApi, storageApi } from '@smartdesk/common/api';
import { useCanvasDownload, useFeedback } from '@smartdesk/common/composables';
import { CELL_ITEM_TYPE } from '@smartdesk/common/constant';
import { useCornerMarkStore } from '@smartdesk/common/stores';

// 元素类型：桌面、页面、页面楼层、坑位
export type ElementType = 'Desktop' | 'Page' | 'PageSection' | 'Cell';

// 页面设计器状态存储
export const usePageDesignerStore = defineStore(
    'page-designer-store',
    () => {
        const feedback = useFeedback();
        const cornerMarkStore = useCornerMarkStore();

        // 模式：页面设计器、桌面设计器；默认为页面设计器
        const mode = ref<'page' | 'desktop'>('page');

        // 业务分组
        const bizGroup = ref<string>('');

        // 桌面编码
        const desktopCode = ref<string>('');

        // 当前导航编码
        const navCode = ref<string>('');

        // 当前页面实体
        const page = ref<Page>({} as Page);

        // 元素 Map
        const elementMap = reactive<Map<string, any>>(new Map());

        const contentMap = reactive<Map<string, ContentData>>(new Map());
        const contentListMap = reactive<Map<string, ContentData[]>>(new Map());

        // 内容查询表单 eg {"movie":["code001"]}
        const contentFormMap = reactive<Map<string, string[]>>(new Map());

        // 坑位索引缓存
        const cellIndexCache = reactive<Map<string, number>>(new Map());

        // 内容数据缓存 - 避免重复计算
        const cellItemDataCache = reactive<Map<string, CellItemData>>(new Map());

        /**
         * eg: 栏目：{dsCode: 栏目code，size：4,pageSectionCode: 当前楼层}
         */
        const contentSectionFormMap = new Map<string, Array<{ dsCode: string; size: number }>>();

        // 坑位到选中坑位元素的映射
        const selectedCellItemMap = reactive<Map<string, string>>(new Map());

        // 选中的元素编码
        const selectedElementCode = ref<string>();

        // 选中元素的类型，默认选中页面类型
        const selectedElementType = ref<ElementType>();

        // 是否展示预删除的页面楼层
        const showDeletedPageSections = ref<boolean>(false);

        // 左侧面板激活的 Tab
        const leftPanelActiveTab = ref<string>('section');

        // 右侧面板激活的 Tab
        const rightPanelActiveTab = ref<string>();

        // 选中的元素
        const selectedElement = computed({
            get: () => {
                if (selectedElementCode.value) {
                    return elementMap.get(selectedElementCode.value);
                }
            },
            set: (value) => {
                if (selectedElementCode.value) {
                    elementMap.set(selectedElementCode.value, value);
                }
            },
        });

        // 选中的页面楼层
        const selectedPageSection = computed<PageSection>({
            get: () => {
                if (selectedElementType.value === 'PageSection') {
                    return selectedElement.value;
                }
            },
            set: (value) => {
                if (selectedElementType.value === 'PageSection') {
                    selectedElement.value = value;
                }
            },
        });

        // 选中的楼层坑位
        const selectedPageCell = computed<PageCell>({
            get: () => {
                if (selectedElementType.value === 'Cell') {
                    return selectedElement.value;
                }
            },
            set: (value) => {
                if (selectedElementType.value === 'Cell') {
                    selectedElement.value = value;
                }
            },
        });

        // 获取当前元素类型
        const getElementTypeName = () => {
            if (!selectedElementType.value) {
                return '';
            }

            switch (selectedElementType.value) {
                case 'Desktop':
                    return '桌面';
                case 'Page':
                    return '页面';
                case 'PageSection':
                    return '页面楼层';
                case 'Cell':
                    return '坑位';
                default:
                    return '';
            }
        };

        // 获取页面实体，设置元素 Map
        const getPage = async (code: string) => {
            const res = await pageApi.getPage(code);
            if (res.code === 200) {
                page.value = res.result;
                await buildElementMap();
                await initCellItemData();
            }
        };

        // 获取页面楼层
        const getPageSection = async (pageSectionCode: string) => {
            const res = await pageSectionApi.getPageSectionByCode(pageSectionCode);
            if (res.code === 200) {
                elementMap.set(pageSectionCode, res.result);
            }
            return res;
        };

        // 更新页面楼层
        const updatePageSection = async (pageSection: PageSection) => {
            // 更新页面楼层
            const res = await pageSectionApi.updatePageSectionData(pageSection.code, pageSection);
            if (res.code === 200) {
                feedback.success('页面楼层更新成功');
                await refreshPageData(page.value.code);

                // 截图
                await capturePageSectionScreenshot(res.result);
            } else {
                feedback.error('页面楼层更新失败：' + res.msg);
            }
            return res;
        };

        // 更新坑位
        const updatePageCell = async (pageCell: PageCell) => {
            const res = await cellApi.updatePageCellData(pageCell.code, pageCell);
            if (res.code === 200) {
                feedback.success('坑位更新成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('坑位更新失败：' + res.msg);
            }
            return res;
        };

        // 构建元素 Map
        const buildElementMap = async () => {
            elementMap.clear();
            cellIndexCache.clear();
            cellItemDataCache.clear();
            selectedPageCellItemCache.clear();

            // 设置页面
            elementMap.set(page.value.code, page.value);

            page.value.pageSectionList?.forEach((pageSection) => {
                // 确保有默认频次
                ensureDefaultScheduleRule(pageSection);
                // 设置页面楼层
                elementMap.set(pageSection.code, pageSection);

                pageSection.pageCellList?.forEach((pageCell, index) => {
                    // 确保坑位有默认坑位元素
                    ensureDefaultCellItem(pageCell);
                    // 对坑位元素进行排序
                    sortCellItems(pageCell);
                    // 设置楼层坑位
                    elementMap.set(pageCell.code, pageCell);

                    // 建立坑位索引缓存
                    cellIndexCache.set(pageCell.code, index);
                });
            });
        };

        // 初始化坑位元素内容
        const initCellItemData = async () => {
            page.value.pageSectionList?.forEach((pageSection) => {
                // 构建 contentSectionFormMap
                if (pageSection.dsCode && pageSection.dsType) {
                    const size = pageSection.pageCellList.length;
                    const entry = {
                        dsCode: pageSection.dsCode,
                        size,
                    };
                    if (contentSectionFormMap.has(pageSection.dsType)) {
                        contentSectionFormMap.get(pageSection.dsType)!.push(entry);
                    } else {
                        contentSectionFormMap.set(pageSection.dsType, [entry]);
                    }
                }

                // 构建 contentFormMap
                pageSection.pageCellList?.forEach((pageCell) => {
                    pageCell.pageCellItemList.forEach((pageCellItem) => {
                        if (pageCellItem.dataType && pageCellItem.dataCode) {
                            if (contentFormMap.has(pageCellItem.dataType)) {
                                // 合并并去重
                                const existingCodes = contentFormMap.get(pageCellItem.dataType)!;
                                const merged = Array.from(new Set([...existingCodes, ...[pageCellItem.dataCode]]));
                                contentFormMap.set(pageCellItem.dataType, merged);
                            } else {
                                // 新建项
                                contentFormMap.set(pageCellItem.dataType, [...[pageCellItem.dataCode]]);
                            }
                        }
                    });
                });
            });

            // 获取内容数据
            await getContent(contentFormMap);

            // 获取楼层内容数据
            await getSectionContent(contentSectionFormMap);
        };

        // 获取内容数据
        const getContent = async (contentFormMap: Map<string, string[]>) => {
            const res = await cmsApi.searchContent(convertStringArrayMapToObject(contentFormMap));
            if (res.code === 200) {
                // 清空旧数据
                contentMap.clear();
                for (const [key, value] of Object.entries(res.result)) {
                    contentMap.set(key, value);
                }
                // 清除缓存，因为内容数据已更新
                clearCellItemDataCache();
            }
        };

        // 获取楼层内容数据
        const getSectionContent = async (
            contentSectionFormMap: Map<string, Array<{ dsCode: string; size: number }>>
        ) => {
            const res = await cmsApi.searchSectionContent(convertDsInfoMapToObject(contentSectionFormMap));
            if (res.code === 200) {
                // 清空旧数据
                contentListMap.clear();
                for (const [key, value] of Object.entries(res.result)) {
                    contentListMap.set(key, value);
                }
                // 清除缓存，因为楼层内容数据已更新
                clearCellItemDataCache();
            }
        };

        /**
         * 批量预处理页面楼层的所有坑位数据
         */
        const batchPreprocessPageSectionCells = (pageSection: PageSection) => {
            pageSection.pageCellList?.forEach((cell) => {
                const cacheKey = `${pageSection.code}-${cell.code}`;

                // 如果已经缓存，跳过
                if (cellItemDataCache.has(cacheKey)) {
                    return;
                }

                // 获取选中的坑位元素
                const item = getSelectedPageCellItem.value(cell.code);
                let cellItemData: CellItemData = item?.itemData ?? {};

                // 获取内容数据
                const contentData = resolveContentData(pageSection, cell, item);
                cellItemData.content = contentData;
                cellItemData.cornerMarks = convertCornerMarks(cellItemData.content?.cornerMarks);

                // 如果当前优先级是页面楼层
                if (getContentDataPriority(pageSection, cell) === 'pageSection') {
                    // 就要从 ContentData 转为 CellItemData
                    cellItemData = convertContentDataToCellItemData(contentData);
                }

                // 缓存结果
                cellItemDataCache.set(cacheKey, cellItemData);
            });
        };

        /**
         * 将 PageCellItem 转换为 CellItemData (优化版本)
         * */
        const convertToCellItemData = (pageSection: PageSection, cell: PageCell): CellItemData => {
            // 生成缓存键
            const cacheKey = `${pageSection.code}-${cell.code}`;

            // 检查缓存
            if (cellItemDataCache.has(cacheKey)) {
                return cellItemDataCache.get(cacheKey)!;
            }

            // 如果缓存未命中，触发批量预处理
            batchPreprocessPageSectionCells(pageSection);

            // 再次检查缓存
            return cellItemDataCache.get(cacheKey) || {};
        };

        /**
         * ContentData 转为 CellItemData
         * */
        const convertContentDataToCellItemData = (contentData: ContentData) => {
            const cellItemData: CellItemData = {
                itemTitle: contentData?.title,
                itemSubTitle: contentData?.subTitle,
                itemType: contentData?.type,
                itemCode: contentData?.code,
                itemIcons: contentData?.pictures,
                dataLinkType: '',
                dataLink: '',
                cornerMarks: convertCornerMarks(contentData?.cornerMarks),
                channelCode: '',
                startTime: '',
                endTime: '',
                content: contentData ?? {},
            };
            return cellItemData;
        };

        // 角标转换
        const convertCornerMarks = (cornerMarks: Record<string, any> | undefined) => {
            if (!cornerMarks) {
                return [];
            }
            const baseMarkCode = cornerMarks.baseMark;
            const opMarkCode = cornerMarks.opMark;
            const codes = [baseMarkCode, opMarkCode].filter(Boolean) as string[];
            // 在函数内部调用 store，确保 Pinia 已经初始化
            return cornerMarkStore.getCornerMarkByCodes(codes);
        };

        /**
         * 图片路径添加前缀
         * */
        const addPathPrefix = (path?: string) => {
            if (!path) {
                return '';
            }

            if (path.startsWith('/') || path.startsWith('http')) {
                return path;
            }
            return '/' + path;
        };

        /**
         * 根据页面楼层、坑位获取优先级
         * */
        const getContentDataPriority = (pageSection: PageSection, cell: PageCell) => {
            // 如果配置了页面楼层数据源
            if (pageSection.dsCode && pageSection.dsType) {
                // 且设置了坑位手动配置，就使用坑位自己的数据
                if (cell.dsType === 'cell') {
                    return 'cell';
                }

                // 且没有设置坑位手动配置，就使用页面楼层的数据
                return 'pageSection';
            }

            // 如果没有配置页面楼层数据源，就使用坑位自己的数据
            return 'cell';
        };

        /**
         * 根据数据源优先级获取 contentData
         */
        const resolveContentData = (
            pageSection: PageSection,
            cell: PageCell,
            item: PageCellItem | null
        ): ContentData => {
            if (!item) return {};

            // 使用缓存的坑位下标，避免 findIndex 操作
            const index = cellIndexCache.get(cell.code) ?? 0;

            const priority = getContentDataPriority(pageSection, cell);
            if (priority === 'cell') {
                // 优先使用坑位数据，使用 toRaw 避免响应式追踪
                return item.dataCode && item.dataType ? (toRaw(contentMap).get(item.dataCode) ?? {}) : {};
            }

            // 优先使用页面楼层数据源
            return getContentDataByTypeAndIndex(pageSection.dsCode, index);
        };

        // 根据类型 和 下标 获取内容数据 (优化版本)
        const getContentDataByTypeAndIndex = (categoryCode: string, index: number) => {
            // 使用 toRaw 避免响应式追踪
            const list = toRaw(contentListMap).get(categoryCode);
            return list ? list[index] : ({} as ContentData);
        };

        // 强制刷新组件
        const forceRefreshComponent = (cellCode: string) => {
            const currentVersion = componentVersionMap.get(cellCode) || 0;
            componentVersionMap.set(cellCode, currentVersion + 1);
        };

        // 获取组件版本号
        const getComponentVersion = (cellCode: string): number => {
            return componentVersionMap.get(cellCode) || 0;
        };

        // 清除缓存
        const clearCellItemDataCache = (pageSectionCode?: string, cellCode?: string) => {
            if (pageSectionCode && cellCode) {
                // 清除特定缓存
                const cacheKey = `${pageSectionCode}-${cellCode}`;
                cellItemDataCache.delete(cacheKey);
                selectedPageCellItemCache.delete(cellCode);
                // 强制刷新对应组件
                forceRefreshComponent(cellCode);
            } else if (pageSectionCode) {
                // 清除页面楼层相关的所有缓存
                const keysToDelete = Array.from(cellItemDataCache.keys()).filter((key) =>
                    key.startsWith(`${pageSectionCode}-`)
                );
                keysToDelete.forEach((key) => cellItemDataCache.delete(key));

                // 清除对应的坑位元素缓存并强制刷新组件
                const pageSection = toRaw(elementMap).get(pageSectionCode);
                if (pageSection) {
                    pageSection.pageCellList?.forEach((cell: PageCell) => {
                        selectedPageCellItemCache.delete(cell.code);
                        forceRefreshComponent(cell.code);
                    });
                }
            } else {
                // 清除所有缓存
                cellItemDataCache.clear();
                selectedPageCellItemCache.clear();
                componentVersionMap.clear();
            }
        };

        // 用于 Map<string, string[]>
        const convertStringArrayMapToObject = (map: Map<string, string[]>) => {
            const obj: Record<string, string[]> = {};
            map.forEach((value, key) => {
                obj[key] = value;
            });
            return obj;
        };

        // 用于 Map<string, Array<{ dsCode, size, pageSectionCode }>>
        const convertDsInfoMapToObject = (
            map: Map<string, Array<{ dsCode: string; size: number }>>
        ): Record<string, Array<{ dsCode: string; size: number }>> => {
            const obj: Record<string, Array<{ dsCode: string; size: number }>> = {};
            map.forEach((value, key) => {
                obj[key] = value;
            });
            return obj;
        };

        // 更新页面楼层排序
        const updatePageSectionOrderNo = async () => {
            const res = await pageSectionApi.updatePageSectionOrder(page.value.pageSectionList);
            if (res.code === 200) {
                feedback.success('保存页面楼层排序成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('保存页面楼层排序失败：' + res.msg);
            }
            return res;
        };

        // 更新页面数据
        const refreshPageData = async (code: string) => {
            await getPage(code);
        };

        // 刷新页面
        const refreshPage = async (code: string) => {
            await refreshPageData(code);

            if (mode.value === 'desktop') {
                switchElement('Desktop', code);
            } else {
                switchElement('Page', code);
            }

            // 初始化
            leftPanelActiveTab.value = 'section';
            rightPanelActiveTab.value = 'props';

            batchEditMode.value = false;
            batchEditCells.value = [];
        };

        // 更新样式信息
        const updateProps = (value: Record<string, any>) => {
            selectedElement.value.layout.props = value;

            // 如果是坑位，强制刷新组件
            if (selectedElement.value.componentType !== 'page' && selectedElement.value.componentType !== 'section') {
                forceRefreshComponent(selectedElement.value.code);
            }
        };

        // 保存样式信息
        const saveProps = async () => {
            let res: any;
            if (selectedElement.value.componentType === 'page') {
                res = await pageApi.updatePageLayout(selectedElement.value.code, selectedElement.value as Page);
            } else if (selectedElement.value.componentType === 'section') {
                res = await pageSectionApi.updatePageSectionLayout(
                    selectedElement.value.code,
                    selectedElement.value as PageSection
                );
            } else {
                res = await cellApi.updatePageCellLayout(selectedElement.value.code, selectedElement.value as PageCell);
            }

            if (res && res.code === 200) {
                feedback.success('更新样式成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('更新样式失败：' + res.msg);
            }
        };

        // 更新元素信息
        const updateElement = async () => {
            if (!selectedElementCode.value) {
                return;
            }

            let res: any;
            switch (selectedElementType.value) {
                case 'Desktop':
                    res = await pageApi.updatePage(selectedElementCode.value, selectedElement.value);
                    break;
                case 'Page':
                    res = await pageApi.updatePage(selectedElementCode.value, selectedElement.value);
                    break;
                case 'PageSection':
                    res = await pageSectionApi.updatePageSectionData(selectedElementCode.value, selectedElement.value);
                    break;
                case 'Cell':
                    res = await cellApi.updatePageCellData(selectedElementCode.value, selectedElement.value);
                    break;
            }

            if (res && res.code === 200) {
                feedback.success('更新成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('更新失败');
            }

            return res;
        };

        // 批量更新坑位样式信息
        const batchUpdateCellLayoutProps = async (
            componentStyleCode: string,
            pageCells: PageCell[],
            value: Record<string, any>
        ) => {
            pageCells.forEach((pageCell) => {
                pageCell.layout.props = value;
            });

            // 将 pageCells 映射为codeLayoutMap
            const codeLayoutMap: Record<string, LayoutFile> = {};
            pageCells.forEach((pageCell) => {
                codeLayoutMap[pageCell.code] = pageCell.layout;
            });

            const res = await cellApi.batchUpdatePageCellLayout(componentStyleCode, codeLayoutMap);
            if (res.code === 200) {
                feedback.success('批量更新坑位样式成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('批量更新坑位样式失败：' + res.msg);
            }
        };

        // 更新坑位组件
        const updateCellComponent = async (componentStyle: ComponentStyle, sync: boolean) => {
            if (!selectedElementCode.value) {
                return;
            }

            // 更新组件类型、样式 props
            selectedElement.value.componentType = componentStyle.type;
            selectedElement.value.componentStyleCode = componentStyle.code;

            // 需要遍历 componentStyle.layout 的所有属性
            selectedElement.value.layout.props = {
                ...selectedElement.value.layout.props,
                ...Object.values(componentStyle.layout).reduce((acc, obj) => ({ ...acc, ...obj }), {}),
            };

            // 调用接口
            if (!sync) {
                return;
            }
            const res = await cellApi.updatePageCellComponent(selectedElementCode.value, selectedElement.value);
            if (res.code === 200) {
                feedback.success('更新坑位组件成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('更新坑位组件失败：' + res.msg);
            }
        };

        // 切换模式
        const switchMode = (modeForm: 'page' | 'desktop') => {
            mode.value = modeForm;
        };

        // 设置页面所属的导航和导航所属的桌面
        const setBizGroupDesktopNav = (bizGroupForm: string, desktopCodeForm: string, navCodeForm: string) => {
            bizGroup.value = bizGroupForm;
            desktopCode.value = desktopCodeForm;
            navCode.value = navCodeForm;
        };

        // 切换元素
        const switchElement = (type: ElementType, code: string) => {
            selectedElementType.value = type;
            selectedElementCode.value = code;
        };

        // 切换坑位元素
        const switchPageCellItem = (itemCode: string) => {
            if (selectedPageCell.value) {
                selectedCellItemMap.set(selectedPageCell.value.code, itemCode);
            }
        };

        // 坑位元素缓存 - 避免重复查找
        const selectedPageCellItemCache = reactive<Map<string, PageCellItem | null>>(new Map());

        // 组件刷新版本号 - 用于强制刷新组件
        const componentVersionMap = reactive<Map<string, number>>(new Map());

        // 根据坑位编码获取选中的坑位元素 (优化版本)
        const getSelectedPageCellItem = computed(() => {
            return (pageCellCode: string): PageCellItem | null => {
                // 检查缓存
                if (selectedPageCellItemCache.has(pageCellCode)) {
                    return selectedPageCellItemCache.get(pageCellCode)!;
                }

                const pageCell = toRaw(elementMap).get(pageCellCode);
                if (!pageCell) {
                    selectedPageCellItemCache.set(pageCellCode, null);
                    return null;
                }

                // 使用 toRaw 避免响应式追踪
                let itemCode = toRaw(selectedCellItemMap).get(pageCellCode);

                // 如果没有用户选择，找默认元素或第一个元素
                if (!itemCode) {
                    const defaultItem = pageCell.pageCellItemList?.find(
                        (item: PageCellItem) => item.defaultFlag === CELL_ITEM_TYPE.DEFAULT
                    );
                    itemCode = defaultItem?.code || pageCell.pageCellItemList?.[0]?.code;
                }

                // 获取到最终的坑位元素编码，根据编码获取坑位元素
                let result: PageCellItem | null = null;
                if (itemCode) {
                    result = pageCell.pageCellItemList?.find((item: PageCellItem) => item.code === itemCode) || null;
                }

                // 缓存结果
                selectedPageCellItemCache.set(pageCellCode, result);
                return result;
            };
        });

        // 切换是否展示预删除的页面楼层
        const switchDeletedPageSections = () => {
            showDeletedPageSections.value = !showDeletedPageSections.value;
        };

        // 新增页面楼层
        const addPageSection = async (sourceCode: string, targetCode: string) => {
            const res = await pageSectionApi.addPageSection(sourceCode, targetCode);
            if (res.code === 200) {
                feedback.success('新增页面楼层成功');
                await refreshPageData(page.value.code);

                // 截图
                await capturePageSectionScreenshot(res.result);
            } else {
                feedback.error('新增页面楼层失败：' + res.msg);
            }
        };

        // 更换页面楼层
        const changePageSection = async (sourceCode: string, targetCode: string) => {
            const res = await pageSectionApi.changePageSection(sourceCode, targetCode);
            if (res.code === 200) {
                feedback.success('更换页面楼层成功');
                await refreshPageData(page.value.code);

                // 截图
                await capturePageSectionScreenshot(res.result);
            } else {
                feedback.error('更换页面楼层失败：' + res.msg);
            }
        };

        // 页面楼层另存为楼层定义
        const pageSectionSaveAsSection = async (pageSectionCode: string, sectionForm: Section) => {
            const res = await pageSectionApi.saveAsSection(pageSectionCode, sectionForm);
            if (res.code === 200) {
                feedback.success('页面楼层另存为楼层定义成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('页面楼层另存为楼层定义失败：' + res.msg);
            }
        };

        // 滚动模式下：新增坑位
        const createPageCell = async (pageSectionCode: string) => {
            // 拿到楼层对应的坑位列表
            const pageSection: PageSection = elementMap.get(pageSectionCode);
            const pageCellList = pageSection.pageCellList || [];

            // 找到 layout.rect.left 最大的坑位
            let maxLeftCell = pageCellList[0];
            if (pageCellList.length > 0) {
                maxLeftCell = pageCellList.reduce((prev, current) => {
                    return prev.layout.rect.left > current.layout.rect.left ? prev : current;
                });
            }

            // 构建新坑位数据对象
            const newCellData: Partial<PageCell> = {
                pageId: pageSection.pageId,
                pageCode: pageSection.pageCode,
                pageSectionId: pageSection.id,
                pageSectionCode: pageSection.code,
                orgId: pageSection.orgId,
                // 如果存在最大 left 的坑位，则基于它创建新的坑位布局
                layout: maxLeftCell
                    ? {
                          rect: {
                              ...maxLeftCell.layout.rect,
                              left:
                                  maxLeftCell.layout.rect.left +
                                  (pageSection.layout.gap ?? 0) +
                                  (pageSection.layout.standardSize?.width ?? maxLeftCell.layout.rect.width),
                          },
                          padding: pageSection.layout.padding,
                          gap: pageSection.layout.gap,
                          mode: pageSection.layout.mode,
                          standardSize: pageSection.layout.standardSize,
                          props: {},
                      }
                    : {
                          rect: { top: 0, left: 0, width: 0, height: 0 },
                          padding: { top: 0, left: 0, right: 0, bottom: 0 },
                          gap: 0,
                          mode: DesignMode.SCROLLABLE,
                          standardSize: { width: 0, height: 0 },
                          props: {},
                      },
            };

            const res = await cellApi.createPageCell(newCellData);
            if (res.code === 200) {
                feedback.success('坑位新增成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('坑位新增失败：' + res.msg);
            }
        };

        // 新增坑位元素
        const createCellItem = async (pageCellItem: PageCellItem) => {
            const res = await cellApi.createPageCellItem(pageCellItem);
            if (res.code === 200) {
                feedback.success('坑位元素新增成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('坑位元素新增失败：' + res.msg);
            }
            return res;
        };

        // 更新坑位元素
        const updateCellItem = async (pageCellItem: PageCellItem) => {
            const res = await cellApi.updatePageCellItem(pageCellItem.code, pageCellItem);
            if (res.code === 200) {
                feedback.success('坑位元素更新成功');
                await refreshPageData(page.value.code);
            } else {
                feedback.error('坑位元素更新失败：' + res.msg);
            }
            return res;
        };

        // 确保页面楼层有默认频次
        const ensureDefaultScheduleRule = (pageSection: PageSection) => {
            if (!pageSection.scheduleRule) {
                pageSection.scheduleRule = {
                    scheduleConfig: {
                        scheduleType: 1,
                        dayList: [],
                        startTime: '',
                        endTime: '',
                    },
                } as unknown as ScheduleRule;
            }
        };

        // 确保坑位有默认坑位元素
        const ensureDefaultCellItem = (pageCell: PageCell) => {
            const hasDefaultItem = pageCell.pageCellItemList?.some(
                (item) => item.defaultFlag === CELL_ITEM_TYPE.DEFAULT
            );

            if (!hasDefaultItem) {
                const defaultItem: PageCellItem = {
                    code: `item_${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                    cellId: pageCell.id,
                    cellCode: pageCell.code,
                    defaultFlag: CELL_ITEM_TYPE.DEFAULT,
                    icons: {},
                    type: CELL_ITEM_TYPE.DEFAULT,
                    orderNo: 0,
                    delFlag: 0,
                    status: 1,
                    auditStatus: 0,
                    visibleStatus: 1,
                    onlineStatus: 0,
                    scheduleRule: {
                        scheduleConfig: {
                            scheduleType: 0,
                            dayList: [],
                            startTime: '',
                            endTime: '',
                        },
                    },
                } as unknown as PageCellItem;

                if (!pageCell.pageCellItemList) {
                    pageCell.pageCellItemList = [];
                }
                pageCell.pageCellItemList.push(defaultItem);
            }
        };

        // 对坑位元素进行排序（默认项在前）
        const sortCellItems = (pageCell: PageCell) => {
            if (!pageCell.pageCellItemList) return;

            pageCell.pageCellItemList.sort((a, b) => {
                if (a.defaultFlag === CELL_ITEM_TYPE.DEFAULT) return -1;
                if (b.defaultFlag === CELL_ITEM_TYPE.DEFAULT) return 1;
                return 0;
            });
        };

        // 基础画布引用
        const baseCanvasRef = ref<any>(null);

        // 页面楼层截图功能
        const capturePageSectionScreenshot = async (pageSection: PageSection) => {
            // 等待 DOM 更新
            await nextTick();

            // 查找对应的页面楼层容器元素
            const targetElement = document.getElementById(`page-section-${pageSection.code}`);
            // 创建临时的 ref 用于截图
            const tempRef = ref<HTMLElement>(targetElement as HTMLElement);

            // 使用 useCanvasDownload 进行截图和上传
            const { uploadToServer } = useCanvasDownload(
                tempRef,
                {
                    format: 'png',
                    backgroundColor: '#ffffff',
                },
                storageApi.uploadFile
            );

            const result = await uploadToServer();
            if (result && result.code === 200) {
                pageSection.icon = result.result;
                const resp = await pageSectionApi.updatePageSectionData(pageSection.code, pageSection);
                if (resp.code === 200) {
                    await refreshPageData(page.value.code);
                }
            }
        };

        // 滚动到指定页面楼层
        const scrollToPageSection = (pageSectionCode: string) => {
            // 先切换元素状态
            switchElement('PageSection', pageSectionCode);

            // 定义滚动执行函数
            const executeScroll = () => {
                const sectionElement = document.getElementById(`section-${pageSectionCode}`);
                if (sectionElement && baseCanvasRef.value) {
                    // 使用 base-canvas 的 updatePositionAndRulers 方法进行滚动
                    baseCanvasRef.value.updatePositionAndRulers(0, -sectionElement.offsetTop);
                }
            };

            // 定义重试逻辑
            const tryScroll = (retryCount = 0) => {
                if (retryCount > 10) return;

                if (!baseCanvasRef.value) {
                    // baseCanvasRef 还没注册，等待后重试
                    setTimeout(() => tryScroll(retryCount + 1), 100);
                    return;
                }

                const sectionElement = document.getElementById(`section-${pageSectionCode}`);
                if (!sectionElement) {
                    // DOM 元素还没渲染，等待后重试
                    setTimeout(() => tryScroll(retryCount + 1), 100);
                    return;
                }

                // 执行滚动
                nextTick(() => {
                    nextTick(executeScroll);
                });
            };

            // 开始尝试滚动
            tryScroll();
        };

        // 注册基础画布引用
        const registerBaseCanvas = (canvas: any) => {
            baseCanvasRef.value = canvas;
        };

        // 页面设计器 tab 切换
        const switchTab = (position: 'left' | 'right', tab: string) => {
            if (position === 'left') {
                leftPanelActiveTab.value = tab;
            } else if (position === 'right') {
                rightPanelActiveTab.value = tab;
            }
        };

        // 批量编辑相关
        const batchEditMode = ref<boolean>(false);
        const batchEditCells = ref<PageCell[]>([]);

        return {
            mode,
            bizGroup,
            desktopCode,
            navCode,
            page,
            elementMap,
            selectedElementCode,
            selectedElementType,
            selectedElement,
            getPage,
            buildElementMap,
            updatePageSectionOrderNo,
            refreshPageData,
            refreshPage,
            updateProps,
            saveProps,
            updateElement,
            batchUpdateCellLayoutProps,
            updateCellComponent,
            switchMode,
            setBizGroupDesktopNav,
            switchElement,
            switchPageCellItem,
            getSelectedPageCellItem,
            showDeletedPageSections,
            switchDeletedPageSections,
            changePageSection,
            addPageSection,
            getPageSection,
            updatePageSection,
            updatePageCell,
            selectedPageSection,
            selectedPageCell,
            selectedCellItemMap,
            createPageCell,
            createCellItem,
            updateCellItem,
            sortCellItems,
            pageSectionSaveAsSection,
            getElementTypeName,
            baseCanvasRef,
            scrollToPageSection,
            registerBaseCanvas,
            capturePageSectionScreenshot,
            leftPanelActiveTab,
            rightPanelActiveTab,
            switchTab,
            batchEditMode,
            batchEditCells,
            convertToCellItemData,
            batchPreprocessPageSectionCells,
            clearCellItemDataCache,
            forceRefreshComponent,
            getComponentVersion,
        };
    },
    {
        persist: {
            enabled: true,
            storeName: 'design-portal-store',
            driver: STORAGE_DRIVER.INDEXED_DB,
            storeKey: 'page-designer-store',
            serializer: {
                serialize: (state: StateTree): any => {
                    return state;
                },
                deserialize: (data: any): StateTree | any => {
                    const stateTree: StateTree = data;

                    // 恢复 elementMap
                    if (stateTree.elementMap) {
                        stateTree.elementMap = reactive(new Map(Object.entries(stateTree.elementMap)));
                    } else {
                        stateTree.elementMap = reactive(new Map());
                    }

                    // 恢复 selectedCellItemMap
                    if (stateTree.selectedCellItemMap) {
                        stateTree.selectedCellItemMap = reactive(
                            new Map(Object.entries(stateTree.selectedCellItemMap))
                        );
                    } else {
                        stateTree.selectedCellItemMap = reactive(new Map());
                    }

                    // 恢复 contentMap
                    if (stateTree.contentMap) {
                        stateTree.contentMap = reactive(new Map(Object.entries(stateTree.contentMap)));
                    } else {
                        stateTree.contentMap = reactive(new Map());
                    }

                    // 恢复 contentListMap
                    if (stateTree.contentListMap) {
                        stateTree.contentListMap = reactive(new Map(Object.entries(stateTree.contentListMap)));
                    } else {
                        stateTree.contentListMap = reactive(new Map());
                    }

                    // 恢复缓存 Map (不持久化，每次重新初始化)
                    stateTree.cellIndexCache = reactive(new Map());
                    stateTree.cellItemDataCache = reactive(new Map());
                    stateTree.selectedPageCellItemCache = reactive(new Map());
                    stateTree.componentVersionMap = reactive(new Map());

                    return stateTree;
                },
            },
        },
    }
);
