<template>
    <div class="flex-auto">
        <div class="flex mt-4 mx-2">
            <el-checkbox-group v-model="selectedFocus" @change="handleFocusChange">
                <el-checkbox v-for="focus in focusEditors" :key="focus.type" :value="focus.type">{{
                    focus.title
                }}</el-checkbox>
            </el-checkbox-group>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref } from 'vue';

    // 聚焦编辑器
    defineOptions({
        name: 'FocusEditor',
    });

    // 参数
    const props = defineProps<{
        focus: Record<string, any>;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'change', name: string, value: Record<string, any>): void;
    }>();

    // 聚焦类型
    const focusEditors = [
        { type: 'shake', title: '抖动' },
        { type: 'scale', title: '缩放' },
        { type: 'flicker', title: '扫光' },
    ];

    // 选择的聚焦类型
    const selectedFocus = ref<string[]>([]);

    // 处理聚焦类型变化
    const handleFocusChange = () => {
        const values: any = {};
        for (let i = 0; i < selectedFocus.value.length; i++) {
            const type = selectedFocus.value[i];
            values[type] = true;
        }
        emit('change', 'focus', values);
    };

    // 初始化已选择的聚焦类型
    onMounted(() => {
        const focus = props.focus;
        if (focus) {
            selectedFocus.value = Object.keys(focus);
        }
    });
</script>
