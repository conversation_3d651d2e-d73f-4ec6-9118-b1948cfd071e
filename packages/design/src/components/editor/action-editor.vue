<template>
    <div class="flex-auto">
        <div class="flex mb-4 mx-2">
            <label class="font-bold flex-none w-20 content-center">行为</label>
            <el-select v-model="type" placeholder="选择行为" class="grow" @change="handleChange">
                <el-option v-for="action in JUMP_TYPE" :label="action.name" :value="action.code" />
            </el-select>
        </div>
        <div class="flex mb-4 mx-2">
            <label class="font-bold flex-none w-20 content-center">值</label>
            <el-input v-model="url" placeholder="请输入行为" @change="handleChange" class="grow" />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { ref } from 'vue';

    // 动作编辑器
    defineOptions({
        name: 'ActionEditor',
    });

    // 参数
    const props = defineProps<{
        action: Record<string, any>;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'change', name: string, value: Record<string, any>): void;
    }>();

    // 跳转类型
    const JUMP_TYPE = [
        { name: '路由', code: 'route' },
        { name: '外部链接', code: 'link' },
        { name: '内置', code: 'inside' },
        { name: '自定义', code: 'custom' },
    ];

    // action 类型
    const type = ref(props.action?.type);

    // action 地址
    const url = ref(props.action?.url);

    // 处理变化
    const handleChange = () => {
        emit('change', 'action', { type: type.value, url: url.value });
    };
</script>
