<template>
    <el-config-provider :locale="zhCn">
        <div class="flex items-center gap-1 w-full">
            <el-input-number
                v-model="localConfig.x"
                :disabled="disabled"
                :precision="0"
                controls-position="right"
                @change="handleChange">
                <template #prefix>
                    <span>X</span>
                </template>
                <template #suffix>
                    <span>px</span>
                </template>
            </el-input-number>
            <el-input-number
                v-model="localConfig.y"
                :disabled="disabled"
                :precision="0"
                controls-position="right"
                @change="handleChange">
                <template #prefix>
                    <span>Y</span>
                </template>
                <template #suffix>
                    <span>px</span>
                </template>
            </el-input-number>
        </div>
    </el-config-provider>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';

    // 位置编辑器：x、y
    defineOptions({
        name: 'LocationEditor',
    });

    // 位置类型
    interface LocationConfig {
        x: number;
        y: number;
    }

    interface Props {
        modelValue?: Partial<LocationConfig>;
        disabled?: Boolean;
    }

    interface Emits {
        (e: 'update:modelValue', value: LocationConfig): void;

        (e: 'change', value: LocationConfig): void;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => ({}),
        disabled: () => false,
    });

    const emit = defineEmits<Emits>();

    // 默认配置
    const defaultConfig: LocationConfig = {
        x: 0,
        y: 0,
    };

    // 本地状态
    const localConfig = ref<LocationConfig>({
        ...defaultConfig,
        ...props.modelValue,
    });

    // 处理变化
    const handleChange = () => {
        emit('update:modelValue', localConfig.value);
        emit('change', localConfig.value);
    };

    // 监听 props 变化，同步到本地状态
    watch(
        () => props.modelValue,
        (newValue) => {
            if (newValue) {
                localConfig.value = { ...defaultConfig, ...newValue };
            }
        },
        { deep: true, immediate: true }
    );
</script>
