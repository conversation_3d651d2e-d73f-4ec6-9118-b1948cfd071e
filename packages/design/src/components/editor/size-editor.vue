<template>
    <el-config-provider :locale="zhCn">
        <div class="flex items-center gap-1 w-full">
            <el-input-number
                v-model="localConfig.width"
                :disabled="disabled"
                :precision="0"
                :min="0"
                controls-position="right"
                @change="handleChange">
                <template #prefix>
                    <span>宽</span>
                </template>
                <template #suffix>
                    <span>px</span>
                </template>
            </el-input-number>

            <el-input-number
                v-model="localConfig.height"
                :disabled="disabled"
                :precision="0"
                :min="0"
                controls-position="right"
                @change="handleChange">
                <template #prefix>
                    <span>高</span>
                </template>
                <template #suffix>
                    <span>px</span>
                </template>
            </el-input-number>
        </div>
    </el-config-provider>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';

    // 尺寸编辑器：宽度、高度
    defineOptions({
        name: 'SizeEditor',
    });

    // 尺寸配置
    interface SizeConfig {
        width: number;
        height: number;
    }

    interface Props {
        modelValue?: Partial<SizeConfig>;
        disabled?: Boolean;
    }

    interface Emits {
        (e: 'update:modelValue', value: SizeConfig): void;

        (e: 'change', value: SizeConfig): void;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => ({}),
        disabled: () => false,
    });

    const emit = defineEmits<Emits>();

    // 默认配置
    const defaultConfig: SizeConfig = {
        width: 100,
        height: 100,
    };

    // 本地状态
    const localConfig = ref<SizeConfig>({
        ...defaultConfig,
        ...props.modelValue,
    });

    // 处理变化
    const handleChange = () => {
        emit('update:modelValue', localConfig.value);
        emit('change', localConfig.value);
    };

    // 监听 props 变化，同步到本地状态
    watch(
        () => props.modelValue,
        (newValue) => {
            if (newValue) {
                localConfig.value = { ...defaultConfig, ...newValue };
            }
        },
        { deep: true, immediate: true }
    );
</script>
