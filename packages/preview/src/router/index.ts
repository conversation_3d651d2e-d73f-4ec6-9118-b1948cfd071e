import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/preview',
        name: 'PREVIEW',
        meta: {
            componentName: 'PREVIEW',
            layout: 'designer',
        },
        component: () => import('@smartdesk/preview/views/preview.vue'),
    },
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

export { routes };
export default router;
