import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';
import { componentRoutes } from './component';
import { componentStyleRoutes } from './component-style';
import { desktopRoutes } from './desktop';
import { pageListRoutes } from './page-list';
import { LinkTypeRoutes } from './link-type';
import { PersonalRuleRoutes } from './personal-rule';
import { sectionRoutes } from './section';
import { layoutRoutes } from './layout';
import { siteRoutes } from './site';
import { UserGroupRoutes } from './user-group';
import { ApkVersionRoutes } from './apk-version.ts';
import { RegionRoutes } from './region';
import { DeviceTypeRoutes } from './device-type';
import { MembershipRoutes } from './membership';
import { BoxRoutes } from './box';

// 路由
const routes: Array<RouteRecordRaw> = [
    ...sectionRoutes,
    ...desktopRoutes,
    ...siteRoutes,
    ...layoutRoutes,
    ...componentRoutes,
    ...componentStyleRoutes,
    ...pageListRoutes,
    ...LinkTypeRoutes,
    ...PersonalRuleRoutes,
    ...UserGroupRoutes,
    ...ApkVersionRoutes,
    ...RegionRoutes,
    ...DeviceTypeRoutes,
    ...MembershipRoutes,
    ...BoxRoutes,
];

const router = createRouter({
    history: createWebHistory(),
    routes: routes,
});

export { routes };
export default router;
