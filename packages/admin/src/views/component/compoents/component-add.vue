<template>
    <el-dialog title="编辑组件" v-model="dialogVisible" width="50%" :before-close="handleCancel">
        <el-form ref="formRef" :model="componentForm" :rules="formRules" label-width="100px" label-suffix=":">
            <el-form-item label="组件名称" prop="name">
                <el-input v-model="componentForm.name" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="组件类别" prop="catalog">
                <el-select v-model="componentForm.catalog" placeholder="请选择" size="default" clearable>
                    <el-option
                        v-for="item in catalogOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="缩略图" prop="icon">
                <image-editor v-model="componentForm.icon" />
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <dimension-selector
                    v-model="componentForm.orgId"
                    placeholder="请选择"
                    :data="orgTree"
                    label-key="name"
                    value-key="id"
                    width="370" />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { computed, onMounted, ref, watch } from 'vue';
    import type { FormInstance } from 'element-plus';
    import { dimensionApi } from '@smartdesk/common/api';
    import { Component, Dimension } from '@smartdesk/common/types';
    import { useEnumStore } from '@chances/portal_common_core';

    // 参数
    const props = defineProps<{
        visible: boolean;
        initialData: Partial<Component>;
    }>();

    // 定义 Emits
    const emit = defineEmits(['update:visible', 'submit', 'update:initialData']);

    // 控制弹窗显示
    const dialogVisible = computed({
        get: () => props.visible,
        set: (val) => emit('update:visible', val),
    });

    const enumStore = useEnumStore();

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 组件类别枚举
    const catalogOptions = ref<
        {
            label: string;
            value: string;
        }[]
    >(enumStore.getOptionsByKey('componentCatalog') || []);

    // 页面表单
    const componentForm = ref<Partial<Component>>({});

    // 表单引用
    const formRef = ref<FormInstance>();

    // 表单验证规则
    const formRules = {
        name: [
            {
                required: true,
                message: '请输入组件名称',
                trigger: 'blur',
            },
        ],
        catalog: [
            {
                required: true,
                message: '请选择组件类别',
                trigger: 'blur',
            },
        ],
    };

    // 取消处理
    const handleCancel = () => {
        componentForm.value = { ...props.initialData };
        dialogVisible.value = false;
    };

    // 提交处理
    const handleSubmit = async () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                emit('submit', { ...componentForm.value });
                dialogVisible.value = false;
            } else {
                console.log('表单校验失败');
            }
        });
    };
    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监听 initialData 变化，填充表单
    watch(
        () => props.initialData,
        (component) => {
            componentForm.value = { ...component };
        },
        { immediate: true }
    );

    onMounted(() => {
        getOrgTree();
    });
</script>
