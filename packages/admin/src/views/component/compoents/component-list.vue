<template>
    <BaseTableWithPagination
        :total="totalElements"
        :current-page="currentPage"
        :page-size="pageSize"
        @update:currentPage="handleCurrentChange"
        @update:pageSize="handleSizeChange">
        <template #table>
            <el-table
                ref="multipleTableRef"
                :data="componentList"
                row-key="id"
                :header-cell-style="{ color: 'black', height: '50px' }"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="70" label="全选" />
                <el-table-column property="name" label="组件名称" min-width="240" show-overflow-tooltip />
                <el-table-column label="预览图" min-width="120">
                    <template #default="{ row }">
                        <el-image
                            :preview-src-list="[row.icon]"
                            hide-on-click-modal
                            preview-teleported
                            style="width: 100px; height: 50px"
                            :src="row.icon">
                            <template #error>
                                <image-error-fallback text="图片损坏" />
                            </template>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column property="category" label="组件分类">
                    <template #default="{ row }">
                        <el-tag v-if="row.category" type="success">
                            {{ enumStore.getLabelByKeyAndValue('componentCategory', row.category) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column property="catalog" label="类别" min-width="120">
                    <template #default="{ row }">
                        <el-tag v-if="row.catalog" type="primary">
                            {{ enumStore.getLabelByKeyAndValue('componentCatalog', row.catalog) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column property="orgId" label="组织" min-width="180">
                    <template #default="{ row }">
                        <div>
                            {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="可用状态" width="120">
                    <template #default="{ row }">
                        <el-switch
                            v-if="row.status === 1"
                            :disabled="
                                !canDisable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.COMPONENT.DISABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                        <el-switch
                            v-else
                            :disabled="
                                !canEnable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.COMPONENT.ENABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column label="修改时间" width="180">
                    <template #default="{ row }">
                        {{ row.modifiedTime ? format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column min-width="160" property="modifiedBy" label="修改人" />
                <el-table-column label="操作" fixed="right" width="300">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-button type="primary" text @click="onClickEdit(row)"> 编辑 </el-button>
                            <el-button
                                :disabled="
                                    !canDeleteByAdmin(row) ||
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.COMPONENT.DELETE, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                type="danger"
                                text
                                @click="onClickDelete(row)">
                                删除
                            </el-button>
                            <el-button
                                :disabled="
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.COMPONENT.EXPORT, {
                                        type: 'org',
                                        value: row.orgId,
                                    }) || exportLock
                                "
                                type="success"
                                text
                                @click="onClickExport(row)">
                                导出
                            </el-button>
                            <el-button link type="info" @click="showComponentStyle(row)"> 样式 </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </BaseTableWithPagination>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { Component, ComponentSearchForm } from '@smartdesk/common/types';
    import { format } from 'date-fns';
    import { componentApi, dimensionApi } from '@smartdesk/common/api';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';
    import { LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import { ADMIN_BIZ_PERMISSION, canDeleteByAdmin, canDisable, canEnable } from '@smartdesk/common/permission';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps<{
        componentSearchForm: Partial<ComponentSearchForm>;
    }>();

    // 事件
    const emit = defineEmits(['update:selection', 'edit', 'show-style']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();
    // pinia store
    const enumStore = useEnumStore();

    // 防抖状态变量
    const exportLock = ref(false);
    const lastExportTime = ref(0);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 组件列表
    const componentList = ref<Component[]>([]);

    // 已选中的组件列表
    const componentSelection = ref<Component[]>([]);

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        findComponentList();
    };

    // 处理当前页变化
    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        findComponentList();
    };

    // 中转已选中组件列表
    const handleSelectionChange = (val: Component[]) => {
        componentSelection.value = val;
        emit('update:selection', val);
    };

    // 启用/禁用组件
    const handleStatusChange = async (row: Component) => {
        const word = row.status === 0 ? '不可用' : '可用';
        if (await feedback.confirm(`确定要${word}该组件吗？`, '确定操作', 'warning')) {
            const response =
                row.status === 0
                    ? await componentApi.batchDisableComponent([row.code])
                    : await componentApi.batchEnableComponent([row.code]);
            if (response.code === 200) {
                feedback.success(`${word}组件成功`);
                await findComponentList();
            } else {
                feedback.error(`${word}组件失败：` + response.msg);
            }
        } else {
            row.status = row.status === 1 ? 0 : 1;
        }
    };

    // 查看样式
    const showComponentStyle = (row: Component) => {
        emit('show-style', row);
    };
    // 编辑
    const onClickEdit = (row: Component) => {
        emit('edit', row);
    };

    // 点击删除
    const onClickDelete = async (row: Component) => {
        if (await feedback.confirm(`确定要删除该组件吗？`, '确认操作', 'warning')) {
            const response = await componentApi.batchDeleteComponent([row.code]);
            if (response.code === 200) {
                feedback.success('删除组件成功');
                await findComponentList();
            } else {
                feedback.error('删除组件失败：' + response.msg);
            }
        }
    };

    // 导出组件
    const onClickExport = async (row: Component) => {
        const now = Date.now();

        if (exportLock.value || now - lastExportTime.value < 30000) {
            feedback.error('请勿频繁操作，请稍后再试');
            return;
        }

        exportLock.value = true;
        lastExportTime.value = now;

        try {
            const blob = await componentApi.exportComponent(row.code);

            // 创建浏览器下载行为
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${row.name}_component.zip`;
            a.click();

            // 清理资源
            URL.revokeObjectURL(url);
            feedback.success(`组件[${row.name}]导出成功`);
            await findComponentList();
        } catch (err) {
            feedback.error(`导出失败`);
        } finally {
            setTimeout(() => {
                exportLock.value = false;
            }, 2000);
        }
    };

    // 查询组件列表数据
    const findComponentList = async () => {
        const response = await componentApi.findComponentPage(props.componentSearchForm, {
            page: currentPage.value - 1,
            size: pageSize.value,
        });
        componentList.value = response.result;
        totalElements.value = Number(response.page.totalElements);
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    watch(
        () => props.componentSearchForm,
        () => {
            findComponentList();
        },
        { deep: true }
    );

    onMounted(() => {
        getOrgOptions();
    });

    // 公开方法给父组件调用
    defineExpose({
        findComponentList,
    });
</script>
