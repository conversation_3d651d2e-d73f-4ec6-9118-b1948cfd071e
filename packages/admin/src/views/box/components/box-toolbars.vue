<template>
    <icon-text-button :icon="Plus" text="新建机顶盒类型" color="#23bcca" @click="onClickAdd" />
    <icon-text-button
        :disabled="!canBatchEnable(boxSelection)"
        :icon="CircleCheck"
        text="批量启用"
        color="#23bcca"
        @click="handleBatchEnable" />
    <icon-text-button
        :disabled="!canBatchDisable(boxSelection)"
        :icon="CircleClose"
        text="批量禁用"
        color="#ff9f25"
        @click="handleBatchDisEnable" />
    <icon-text-button
        :disabled="!canBatchDeleteByAdmin(boxSelection)"
        :icon="Delete"
        text="批量删除"
        color="#ed5665"
        @click="handleBatchDelete" />
    <el-button
        :disabled="!canBatchAudit(boxSelection) || !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.BOX.AUDIT)"
        type="primary"
        icon="Promotion"
        @click="handleBatchAudit()">
        批量送审
    </el-button>
</template>

<script setup lang="ts">
    import { CircleCheck, CircleClose, Delete, Plus } from '@element-plus/icons-vue';
    import { Box } from '@smartdesk/common/types';
    import { boxApi, publishApi } from '@smartdesk/common/api';
    import {
        ADMIN_BIZ_PERMISSION,
        canBatchAudit,
        canBatchDeleteByAdmin,
        canBatchDisable,
        canBatchEnable,
    } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';
    import { computed } from 'vue';

    // 参数
    const props = defineProps<{
        boxSelection: Box[];
    }>();

    // 事件
    const emit = defineEmits(['refreshBoxList', 'add']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 楼层布局编码列表
    const codes = computed(() => {
        return props.boxSelection.map((item) => item.code);
    });

    // 批量删除
    const handleBatchDelete = async () => {
        if (await feedback.confirm(`确定要批量删除机顶盒吗？`, '确认操作', 'warning')) {
            const response = await boxApi.batchDeleteBox(codes.value);
            if (response.code === 200) {
                feedback.success('批量删除成功');
                refreshLayoutList();
            } else {
                feedback.error('批量删除失败：' + response.msg);
            }
        }
    };

    // 批量启用
    const handleBatchEnable = async () => {
        if (await feedback.confirm(`确定要批量启用机顶盒吗？`, '确认操作', 'warning')) {
            const response = await boxApi.batchEnableBox(codes.value);
            if (response.code === 200) {
                feedback.success('批量启用成功');
                refreshLayoutList();
            } else {
                feedback.error('批量启用失败：' + response.msg);
            }
        }
    };

    // 批量禁用
    const handleBatchDisEnable = async () => {
        if (await feedback.confirm(`确定要批量禁用机顶盒吗？`, '确认操作', 'warning')) {
            const response = await boxApi.batchDisableBox(codes.value);
            if (response.code === 200) {
                feedback.success('批量禁用成功');
                refreshLayoutList();
            } else {
                feedback.error('批量禁用失败：' + response.msg);
            }
        }
    };

    // 刷新列表
    const refreshLayoutList = () => {
        emit('refreshBoxList');
    };

    // 新建机顶盒
    const onClickAdd = () => {
        emit('add');
    };

    // 批量发布
    const handleBatchAudit = async () => {
        if (await feedback.confirm('确定要批量送审吗？', '警告', 'warning')) {
            const res = await publishApi.batchPublishSelf('Box', codes.value, 'CREATE');
            if (res.code === 200) {
                feedback.success('批量送审成功');
                emit('refreshBoxList');
            } else {
                feedback.error('批量送审失败：' + res.msg);
            }
        }
    };
</script>

<style scoped></style>
