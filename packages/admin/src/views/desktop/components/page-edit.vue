<template>
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="30%" :before-close="handleCancel">
        <el-form ref="formRef" :model="pageForm" :rules="formRules" label-width="80px">
            <el-form-item label="页面名称" prop="name">
                <el-input v-model="pageForm.name" placeholder="请输入页面名称" clearable />
            </el-form-item>
            <el-form-item label="分辨率" prop="resolution">
                <el-select v-model="pageForm.resolution" placeholder="请选择分辨率" size="default" clearable>
                    <el-option
                        v-for="item in resolutionOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <tree-selector
                    v-model="pageForm.orgId"
                    placeholder="请选择组织"
                    :data="orgTree"
                    label-key="name"
                    value-key="id"
                    width="370" />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, nextTick, onMounted, ref, watch } from 'vue';
    import type { FormInstance } from 'element-plus';
    import { Dimension, Page } from '@smartdesk/common/types';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { dimensionApi } from '@smartdesk/common/api';

    // 枚举
    const enumStore = useEnumStore();

    // 分辨率状态枚举
    const resolutionOption = ref<Enumeration[]>(enumStore.getEnumsByKey('resolution') || []);

    // 参数
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        initialData: {
            type: Object,
            required: false,
        },
    });

    // 事件
    const emit = defineEmits(['update:visible', 'submit', 'update:initialData']);

    // 表单验证规则
    const formRules = {
        name: [
            {
                required: true,
                message: '请输入页面名称',
                trigger: 'blur',
            },
        ],
        resolution: [
            {
                required: true,
                message: '请选择分辨率',
                trigger: 'blur',
            },
        ],
    };

    // 控制弹窗显示
    const dialogVisible = computed({
        get: () => props.visible,
        set: (val) => emit('update:visible', val),
    });

    // 表单引用
    const formRef = ref<FormInstance>();

    // 页面表单
    const pageForm = ref<Partial<Page>>({});

    // 弹窗标题
    const dialogTitle = computed(() => (props.initialData?.id ? '编辑页面' : '新增页面'));

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 取消处理
    const handleCancel = () => {
        dialogVisible.value = false;
    };

    // 提交处理
    const handleSubmit = async () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                emit('submit', { ...pageForm.value });
                emit('update:initialData', undefined);
            } else {
                console.log('表单校验失败');
            }
        });
    };

    // 重置表单
    const resetForm = () => {
        pageForm.value = {};
        formRef.value?.clearValidate();
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监听visible变化初始化表单
    watch(
        () => props.visible,
        async (visible) => {
            if (visible) {
                await nextTick();
                if (props.initialData?.id) {
                    pageForm.value = { ...props.initialData };
                } else {
                    resetForm();
                }
            }
        }
    );

    onMounted(() => {
        getOrgTree();
    });
</script>
