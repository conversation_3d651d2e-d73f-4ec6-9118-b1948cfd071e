<template>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        <el-card
            v-for="(desktop, index) in desktopList"
            :key="index"
            shadow="never"
            class="hover:shadow-md transition-shadow duration-300 desktop-card">
            <div class="flex flex-col h-full">
                <div class="w-full aspect-video mb-2 overflow-hidden rounded-lg">
                    <image-card
                        hide-on-click-modal
                        :src="desktop.icon"
                        :with-prefix="false"
                        :title="desktop.name"
                        :resolution="enumStore.getLabelByKeyAndValue('resolution', desktop.resolution)"
                        fit="cover"
                        class="w-full h-full object-cover"
                        :draggable="false" />
                </div>

                <div class="flex items-center gap-2 text-sm place-content-between mb-2">
                    <status-dot :type="getOnlineStatusDotStatus(desktop.onlineStatus)">
                        {{ enumStore.getLabelByKeyAndValue('onlineStatus', desktop.onlineStatus) }}
                    </status-dot>
                    <status-dot :type="getAuditStatusDotStatus(desktop.auditStatus)">
                        {{ enumStore.getLabelByKeyAndValue('auditStatus', desktop.auditStatus) }}
                    </status-dot>
                    <status-dot :type="getDelFlagDotStatus(desktop.delFlag)">
                        {{ enumStore.getLabelByKeyAndValue('delFlag', desktop.delFlag) }}
                    </status-dot>
                    <status-dot :type="getStatusDotStatus(desktop.status)">
                        {{ enumStore.getLabelByKeyAndValue('enableStatus', desktop.status) }}
                    </status-dot>
                    <status-dot :type="getVisibleStatusDotStatus(desktop.visibleStatus)">
                        {{ enumStore.getLabelByKeyAndValue('visibleStatus', desktop.visibleStatus) }}
                    </status-dot>
                </div>

                <div class="flex flex-row gap-3 toolbar">
                    <el-button type="primary" @click="onClickDesign(desktop)" class="w-full"> 配置 </el-button>
                    <el-button type="success" @click="onClickNav(desktop)" class="w-full"> 导航 </el-button>
                    <el-dropdown :hide-on-click="false" class="w-full">
                        <el-button type="info" class="w-full">
                            <div class="flex items-center justify-center">
                                更多
                                <el-icon class="el-icon--right ml-1">
                                    <ArrowDown />
                                </el-icon>
                            </div>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item>
                                    <el-button type="success" @click="onClickNavGroup(desktop)" class="w-full">
                                        导航分组
                                    </el-button>
                                </el-dropdown-item>
                                <el-dropdown-item>
                                    <el-button
                                        :disabled="
                                            !canAudit(desktop) ||
                                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.DESKTOP.AUDIT, {
                                                type: 'org',
                                                value: desktop.orgId,
                                            })
                                        "
                                        type="primary"
                                        @click="onClickPublish(desktop)"
                                        class="w-full">
                                        送审
                                    </el-button>
                                </el-dropdown-item>
                                <el-dropdown-item>
                                    <el-button
                                        :disabled="
                                            !canDelete(desktop) ||
                                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.DESKTOP.DELETE, {
                                                type: 'org',
                                                value: desktop.orgId,
                                            })
                                        "
                                        type="danger"
                                        @click="onClickDelete(desktop)"
                                        class="w-full">
                                        删除
                                    </el-button>
                                </el-dropdown-item>
                                <el-dropdown-item v-if="canOnline(desktop)">
                                    <el-button
                                        :disabled="
                                            !canOnline(desktop) ||
                                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.DESKTOP.ONLINE, {
                                                type: 'org',
                                                value: desktop.orgId,
                                            })
                                        "
                                        type="success"
                                        @click="onClickOnline(desktop)"
                                        class="w-full">
                                        上线
                                    </el-button>
                                </el-dropdown-item>
                                <el-dropdown-item v-if="canOffline(desktop)">
                                    <el-button
                                        :disabled="
                                            !canOffline(desktop) ||
                                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.DESKTOP.OFFLINE, {
                                                type: 'org',
                                                value: desktop.orgId,
                                            })
                                        "
                                        type="danger"
                                        @click="onClickOffline(desktop)"
                                        class="w-full">
                                        下线
                                    </el-button>
                                </el-dropdown-item>
                                <el-dropdown-item v-if="canEnable(desktop)">
                                    <el-button
                                        :disabled="
                                            !canEnable(desktop) ||
                                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.DESKTOP.ENABLE, {
                                                type: 'org',
                                                value: desktop.orgId,
                                            })
                                        "
                                        type="primary"
                                        @click="onClickEnable(desktop)"
                                        class="w-full">
                                        启用
                                    </el-button>
                                </el-dropdown-item>
                                <el-dropdown-item v-if="canDisable(desktop)">
                                    <el-button
                                        :disabled="
                                            !canDisable(desktop) ||
                                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.DESKTOP.DISABLE, {
                                                type: 'org',
                                                value: desktop.orgId,
                                            })
                                        "
                                        type="warning"
                                        @click="onClickDisable(desktop)"
                                        class="w-full">
                                        禁用
                                    </el-button>
                                </el-dropdown-item>
                                <el-dropdown-item>
                                    <el-button
                                        :disabled="
                                            !canEdit(desktop) ||
                                            !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.DESKTOP.EDIT, {
                                                type: 'org',
                                                value: desktop.orgId,
                                            })
                                        "
                                        type="warning"
                                        @click="onClickEdit(desktop)"
                                        class="w-full">
                                        编辑
                                    </el-button>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
        </el-card>

        <el-card
            v-if="permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.DESKTOP.CREATE)"
            shadow="never"
            @click="onClickAdd"
            class="flex items-center justify-center h-full min-h-[250px] cursor-pointer hover:shadow-md transition-shadow duration-300">
            <div class="flex flex-col items-center justify-center">
                <el-icon size="36" color="gray" class="mb-2">
                    <Plus />
                </el-icon>
                <span class="text-gray-500">添加桌面</span>
            </div>
        </el-card>
    </div>
</template>

<script setup lang="ts">
    import { PropType } from 'vue';
    import { Page } from '@smartdesk/common/types';
    import { ArrowDown, Plus } from '@element-plus/icons-vue';
    import { useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDelete,
        canDisable,
        canEdit,
        canEnable,
        canOffline,
        canOnline,
    } from '@smartdesk/common/permission';

    // 参数
    const props = defineProps({
        desktopList: {
            type: Array as PropType<Page[]>,
            required: true,
        },
    });

    // 事件
    const emit = defineEmits([
        'design',
        'publish',
        'edit',
        'nav',
        'navGroup',
        'delete',
        'add',
        'online',
        'offline',
        'enable',
        'disable',
    ]);

    // pinia store
    const permissionStore = usePermissionStore();
    const enumStore = useEnumStore();

    // 配置：跳转桌面设计器
    const onClickDesign = (desktop: Page) => emit('design', desktop);

    // 送审
    const onClickPublish = (desktop: Page) => emit('publish', desktop);

    // 呼出导航分组
    const onClickNavGroup = (desktop: Page) => emit('navGroup', desktop);

    // 编辑
    const onClickEdit = (desktop: Page) => emit('edit', desktop);

    // 呼出导航
    const onClickNav = (desktop: Page) => emit('nav', desktop);

    // 删除
    const onClickDelete = (desktop: Page) => emit('delete', desktop);

    // 上线
    const onClickOnline = (desktop: Page) => emit('online', desktop);

    // 下线
    const onClickOffline = (desktop: Page) => emit('offline', desktop);

    // 启用
    const onClickEnable = (desktop: Page) => emit('enable', desktop);

    // 停用
    const onClickDisable = (desktop: Page) => emit('disable', desktop);

    // 新增
    const onClickAdd = () => emit('add');

    // 获取审核状态的dot状态
    const getAuditStatusDotStatus = (auditStatus: number) => {
        switch (auditStatus) {
            case 0:
                return 'default';
            case 1:
                return 'warning';
            case 2:
                return 'success';
            case 3:
                return 'error';
            default:
                return 'info';
        }
    };

    // 获取上线状态的dot状态
    const getOnlineStatusDotStatus = (onlineStatus: number) => {
        switch (onlineStatus) {
            case 0:
                return 'default';
            case 1:
                return 'success';
            case 2:
                return 'error';
            default:
                return 'info';
        }
    };

    // 获取可用状态的dot状态
    const getStatusDotStatus = (status: number) => {
        switch (status) {
            case 0:
                return 'error';
            case 1:
                return 'success';
            default:
                return 'info';
        }
    };

    // 获取删除状态的dot状态
    const getDelFlagDotStatus = (delFlag: number) => {
        switch (delFlag) {
            case 0:
                return 'success';
            case -1:
                return 'warning';
            case -2:
                return 'error';
            default:
                return 'info';
        }
    };

    // 获取可见状态的dot状态
    const getVisibleStatusDotStatus = (visibleStatus: number) => {
        switch (visibleStatus) {
            case 0:
                return 'error';
            case 1:
                return 'success';
            default:
                return 'info';
        }
    };
</script>

<style scoped>
    .desktop-card :deep(.el-card__body) {
        padding: 10px;
    }

    .toolbar :deep(.el-button + .el-button) {
        margin-left: 0;
    }
</style>
