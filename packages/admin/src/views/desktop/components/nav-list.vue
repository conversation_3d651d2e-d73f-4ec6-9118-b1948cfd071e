<template>
    <el-table
        :data="navList"
        row-key="id"
        :header-cell-style="{ color: 'black', height: '50px' }"
        :tree-props="{ children: 'children', checkStrictly: true }"
        style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="70" label="全选" />
        <el-table-column property="title" label="导航名称" width="180" show-overflow-tooltip>
            <template #default="{ row }">
                {{ row.title }}
                {{ row.children?.length ? '（' + row.children.length + '）' : '' }}
            </template>
        </el-table-column>
        <el-table-column property="pageCode" label="页面名称" width="180" show-overflow-tooltip>
            <template #default="{ row }">
                <el-button type="primary" link @click="onClickPage(row)">
                    {{ pageList.find((item) => item.code === row.pageCode)?.name }}
                </el-button>
            </template>
        </el-table-column>
        <el-table-column property="orderNo" label="顺序" width="100" />
        <el-table-column property="type" label="导航类型" width="100">
            <template #default="{ row }">
                <div>
                    {{ enumStore.getLabelByKeyAndValue('navType', row.type) }}
                </div>
            </template>
        </el-table-column>
        <el-table-column property="menuType" label="菜单类型" width="100">
            <template #default="{ row }">
                <div>
                    {{ enumStore.getLabelByKeyAndValue('menuType', row.menuType) }}
                </div>
            </template>
        </el-table-column>
        <el-table-column property="defaultPageFlag" label="默认页面" width="100">
            <template #default="{ row }">
                <div>{{ row.defaultPageFlag ? '是' : '否' }}</div>
            </template>
        </el-table-column>

        <el-table-column v-for="imageType in navImageTypes" :key="imageType.code" :label="imageType.name" width="100">
            <template #default="{ row }">
                <el-image
                    v-if="row.icons[imageType.code]"
                    :src="row.icons[imageType.code]"
                    class="h-14"
                    fit="contain"
                    :preview-src-list="[row.icons[imageType.code]]"
                    hide-on-click-modal
                    preview-teleported>
                    <template #error>
                        <image-error-fallback text="图标损坏" />
                    </template>
                </el-image>
            </template>
        </el-table-column>
        <el-table-column property="ruleCode" label="推荐策略" width="180" show-overflow-tooltip>
            <template #default="{ row }">
                <div>
                    {{ personalRuleList.find((item) => item.code === row.ruleCode)?.name }}
                </div>
            </template>
        </el-table-column>
        <el-table-column property="inverted" label="是否反选" width="100">
            <template #default="{ row }">
                <div>{{ row.inverted ? '是' : '否' }}</div>
            </template>
        </el-table-column>
        <el-table-column property="orgId" label="组织" width="180">
            <template #default="{ row }">
                <div>
                    {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                </div>
            </template>
        </el-table-column>
        <el-table-column label="可用状态" width="100">
            <template #default="{ row }">
                <el-switch
                    v-if="row.status === 1"
                    :disabled="
                        !canDisable(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.DISABLE, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    v-model="row.status"
                    size="small"
                    :active-value="1"
                    :inactive-value="0"
                    :inactive-text="row.status ? '可用' : '不可用'"
                    @change="handleStatusChange(row)" />
                <el-switch
                    v-else
                    :disabled="
                        !canEnable(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.ENABLE, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    v-model="row.status"
                    size="small"
                    :active-value="1"
                    :inactive-value="0"
                    :inactive-text="row.status ? '可用' : '不可用'"
                    @change="handleStatusChange(row)" />
            </template>
        </el-table-column>
        <el-table-column label="状态" width="280">
            <template #default="{ row }">
                <status-columns :publishStatus="row" />
            </template>
        </el-table-column>
        <el-table-column label="修改时间" width="180">
            <template #default="{ row }">
                {{ format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') }}
            </template>
        </el-table-column>
        <el-table-column property="modifiedBy" label="修改人" width="100" />
        <el-table-column label="操作" fixed="right" width="420">
            <template #default="{ row }">
                <el-button
                    :disabled="
                        !canEdit(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.EDIT, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    type="primary"
                    text
                    @click="onClickEdit(row)">
                    编辑
                </el-button>
                <el-button
                    :disabled="
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.ADD_CHILD_NAV, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    v-if="row.type === 1"
                    type="primary"
                    text
                    @click="onClickAddChildNav(row)">
                    新增子导航
                </el-button>
                <el-button
                    :disabled="
                        !canAudit(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.AUDIT, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    type="primary"
                    text
                    @click="onClickPublish(row)">
                    送审
                </el-button>
                <el-button
                    :disabled="
                        !canOnline(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.ONLINE, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    v-if="canOnline(row)"
                    type="primary"
                    text
                    @click="onClickOnline(row)">
                    上线
                </el-button>
                <el-button
                    :disabled="
                        !canOffline(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.OFFLINE, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    v-if="canOffline(row)"
                    type="warning"
                    text
                    @click="onClickOffline(row)">
                    下线
                </el-button>
                <el-button
                    :disabled="
                        !canDelete(row) ||
                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.DELETE, {
                            type: 'org',
                            value: row.orgId,
                        })
                    "
                    type="danger"
                    text
                    @click="onClickDelete(row)">
                    删除
                </el-button>
            </template>
        </el-table-column>
    </el-table>
</template>

<script setup lang="ts">
    import { onMounted, PropType, ref, watch } from 'vue';
    import { Nav, NavSearchForm, Page, PersonalRule } from '@smartdesk/common/types';
    import { format } from 'date-fns';
    import { dimensionApi, navApi, pageApi, personalRuleApi, publishApi } from '@smartdesk/common/api';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { useFeedback, useJumpToDesigner } from '@smartdesk/common/composables';
    import { Enumeration, LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDelete,
        canDisable,
        canEdit,
        canEnable,
        canOffline,
        canOnline,
    } from '@smartdesk/common/permission';

    defineOptions({
        name: 'NavList',
    });

    // 参数
    const props = defineProps({
        sectionForm: {
            type: Object as PropType<Partial<NavSearchForm>>,
            default: () => ({}),
        },
        desktop: {
            type: Object as PropType<Page>,
            required: true,
        },
    });

    // 事件
    const emits = defineEmits(['update:selection', 'edit', 'addChildNav']);

    // 网站 pinia
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();
    const { jumpToDesigner } = useJumpToDesigner();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 导航列表
    const navList = ref<Nav[]>([]);

    // 页面列表
    const pageList = ref<Page[]>([]);

    // 推荐策略列表
    const personalRuleList = ref<PersonalRule[]>([]);

    // 选中的导航
    const selectedNavs = ref<Nav[]>([]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 图片类型
    const navImageTypes = ref<Enumeration[]>(enumStore.getEnumsByKey('navImageTypeEnum') || []);

    const handleSelectionChange = (val: Nav[]) => {
        selectedNavs.value = val;
        emits('update:selection', val);
    };

    // 启用/禁用导航
    const handleStatusChange = async (row: Nav) => {
        const word = row.status === 0 ? '禁用' : '启用';
        if (await feedback.confirm(`确定要${word}该导航吗？`, '确定操作', 'warning')) {
            const response = row.status === 0 ? await navApi.disableNav(row.code) : await navApi.enableNav(row.code);
            if (response.code === 200) {
                feedback.success(`${word}导航成功`);
                await getNavTree();
            } else {
                feedback.error(`${word}导航失败：` + response.msg);
            }
        } else {
            row.status = row.status === 1 ? 0 : 1;
        }
    };

    // 点击页面
    const onClickPage = (row: Nav) => {
        jumpToDesigner('page', row.pageCode, {
            desktopCode: row.ownerPageCode,
            navCode: row.code,
            bizGroup: props.desktop?.bizGroup,
        });
    };

    // 点击编辑
    const onClickEdit = (row: Nav) => {
        emits('edit', row);
    };

    // 点击新增子导航
    const onClickAddChildNav = (row: Nav) => {
        emits('addChildNav', row);
    };

    // 点击删除
    const onClickDelete = async (row: Nav) => {
        if (await feedback.confirm(`确定要删除该导航吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishComplete('Nav', row.code, 'DELETE');
            if (res.code === 200) {
                feedback.success('删除导航成功');
                await getNavTree();
            } else {
                feedback.error('删除导航失败：' + res.msg);
            }
        }
    };

    // 点击送审
    const onClickPublish = async (row: Nav) => {
        if (await feedback.confirm(`确定要送审该导航吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishSelf('Nav', row.code, 'CREATE');
            if (res.code === 200) {
                feedback.success('送审导航成功');
                await getNavTree();
            } else {
                feedback.error('送审导航失败：' + res.msg);
            }
        }
    };

    // 点击上线
    const onClickOnline = async (row: Nav) => {
        if (await feedback.confirm(`确定要上线该导航吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishSelf('Nav', row.code, 'ONLINE');
            if (res.code === 200) {
                feedback.success('上线导航成功');
                await getNavTree();
            } else {
                feedback.error('上线导航失败：' + res.msg);
            }
        }
    };

    // 点击下线
    const onClickOffline = async (row: Nav) => {
        if (await feedback.confirm(`确定要下线该导航吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishSelf('Nav', row.code, 'OFFLINE');
            if (res.code === 200) {
                feedback.success('下线导航成功');
                await getNavTree();
            } else {
                feedback.error('下线导航失败：' + res.msg);
            }
        }
    };

    // 查询导航树
    const getNavTree = async () => {
        try {
            const response = await navApi.findNavTree(props.sectionForm);
            if (response.code === 200) {
                const processTree = (nodes: Nav[]) => {
                    nodes.forEach((node: any) => {
                        node.children = node.children || [];
                        if (node.children.length > 0) {
                            processTree(node.children);
                        }
                    });
                };

                navList.value = response.result;
                processTree(navList.value);
            } else {
                feedback.error('获取导航树失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('获取导航树失败');
        }
    };

    // 查询页面列表
    const getPageList = async () => {
        const res = await pageApi.getPageList({ siteCode: siteStore.currentSiteCode }, { paged: false });
        if (res.code === 200) {
            pageList.value = res.result;
        }
    };

    // 查询推荐策略列表
    const getPersonalRuleList = async () => {
        const res = await personalRuleApi.getPersonalRuleList({}, { paged: false });
        if (res.code === 200) {
            personalRuleList.value = res.result;
        }
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    watch(
        () => props.sectionForm,
        (newVal) => {
            // 只有网站编码、桌面编码都存在时，才查询导航列表
            if (newVal.siteCode && newVal.desktopCode) {
                getNavTree();
            }
        },
        { deep: true, immediate: true }
    );

    watch(
        () => siteStore.currentSiteCode,
        () => {
            getPageList();
            getPersonalRuleList();
        },
        { deep: true, immediate: true }
    );

    onMounted(() => {
        getOrgOptions();
    });

    defineExpose({
        getNavTree,
        getPageList,
        selectedNavs,
    });
</script>
