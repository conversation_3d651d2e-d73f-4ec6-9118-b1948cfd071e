<template>
    <el-dialog v-model:model-value="dialogVisible" title="新建桌面" destroy-on-close>
        <template #header>
            <div class="flex">
                <div class="text-xl mr-2">
                    {{ desktopForm.code ? '编辑桌面' : '新建桌面' }}
                </div>
                <div class="text-base">{{ siteStore.currentSite?.name }}</div>
            </div>
        </template>

        <el-form label-width="auto" :rules="rules" :model="desktopForm" ref="desktopAddFormRef" label-suffix=":">
            <el-form-item label="桌面名称" prop="name">
                <el-input v-model:model-value="desktopForm.name" maxlength="200" show-word-limit clearable />
            </el-form-item>
            <el-form-item label="业务分组" prop="bizGroup">
                <radio-input
                    v-model:model-value="desktopForm.bizGroup"
                    :options="desktopGroupList"
                    placeholder="请输入新分组"
                    :input="false" />
            </el-form-item>
            <el-form-item label="分辨率" prop="resolution">
                <el-select v-model="desktopForm.resolution" placeholder="请选择分辨率" size="default" clearable>
                    <el-option
                        v-for="item in resolutionOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="缩略图" prop="icon">
                <image-editor v-model="desktopForm.icon" />
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <dimension-selector
                    v-model="desktopForm.orgId"
                    placeholder="请选择组织"
                    :data="orgTree"
                    label-key="name"
                    value-key="id"
                    width="370" />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="onClickCancel">取消</el-button>
                <el-button type="primary" @click="onClickConfirm">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, onMounted, PropType, ref } from 'vue';
    import { Dimension, Page } from '@smartdesk/common/types';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { Enumeration, LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { FormInstance } from 'element-plus';
    import { dimensionApi } from '@smartdesk/common/api';

    // 参数
    const props = defineProps({
        modelValue: {
            type: Boolean,
            default: true,
        },
        desktop: {
            type: Object as PropType<Partial<Page>>,
            default: true,
        },
    });

    // 事件
    const emit = defineEmits(['update:modelValue', 'update:desktop', 'onClickConfirm', 'onClickCancel']);

    // 网站 store
    const siteStore = useSiteStore();
    // 枚举 store
    const enumStore = useEnumStore();

    // 分辨率枚举
    const resolutionOption = ref<Enumeration[]>(enumStore.getEnumsByKey('resolution') || []);

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 对话框显隐
    const dialogVisible = computed({
        get: () => props.modelValue,
        set: (val) => emit('update:modelValue', val),
    });

    // 桌面表单
    const desktopForm = computed({
        get: () => props.desktop,
        set: (val) => emit('update:desktop', val),
    });

    // 桌面表单引用
    const desktopAddFormRef = ref<FormInstance | null>(null);

    // 分组列表
    const desktopGroupList = ref<LabelValue[]>(
        enumStore.getEnumsByKey('bizGroup')?.map((item) => {
            return {
                label: item.name,
                value: item.code,
            };
        }) || []
    );

    // 表单校验规则
    const rules = {
        name: [
            {
                required: true,
                message: '请输入桌面名称',
                trigger: 'blur',
            },
        ],
        bizGroup: [
            {
                required: true,
                message: '请选择业务分组',
                trigger: ['blur', 'change'],
            },
        ],
        layoutCode: [
            {
                required: true,
                message: '请选择布局',
                trigger: ['blur', 'change'],
            },
        ],
        resolution: [
            {
                required: true,
                message: '请选择分辨率',
                trigger: 'blur',
            },
        ],
    };

    // 点击取消
    const onClickCancel = () => {
        dialogVisible.value = false;
        emit('onClickCancel');
    };

    // 点击确认
    const onClickConfirm = () => {
        (desktopAddFormRef.value as FormInstance).validate((valid) => {
            if (valid) {
                emit('onClickConfirm');
            }
        });
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    onMounted(() => {
        getOrgTree();
    });
</script>
