<template>
    <el-drawer v-model="addVisible" title="添加导航" size="95%" @close="onClose">
        <div class="nav-table-container">
            <icon-text-button
                :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.CREATE)"
                text="新建导航"
                color="#23bcca"
                @click="onClickAdd" />
            <icon-text-button
                :disabled="
                    !canBatchAudit(selectedNavs) ||
                    !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.NAV.BATCH_AUDIT, {
                        type: 'org',
                        value: selectedNavs.filter((nav) => nav.orgId).map((nav) => nav.orgId),
                    })
                "
                :icon="CircleCheck"
                text="批量送审"
                color="#23bcca"
                @click="onClickBatchPublish" />
            <el-table
                :data="navList"
                row-key="id"
                :header-cell-style="{ color: 'black', height: '50px' }"
                :tree-props="{ children: 'children', checkStrictly: true }"
                style="width: 100%; height: calc(100vh - 240px)"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="70" label="全选" />
                <el-table-column property="title" label="导航名称" width="200" show-overflow-tooltip>
                    <template #default="{ row }">
                        <el-input v-model="row.title" size="small" placeholder="请输入导航标题" style="width: 150px" />
                    </template>
                </el-table-column>
                <el-table-column property="pageCode" label="页面名称" width="280" show-overflow-tooltip>
                    <template #default="{ row }">
                        <el-select
                            v-if="row.pageCode"
                            v-model="row.pageCode"
                            placeholder="请选择页面"
                            size="small"
                            clearable
                            @click="getPageList"
                            style="width: 150px">
                            <el-option
                                v-for="item in pageList"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code" />
                        </el-select>
                        <el-input
                            v-if="row.page.name && !row.pageCode"
                            v-model="row.page.name"
                            size="small"
                            disabled
                            style="width: 150px" />
                        <el-button type="primary" text @click="handleSavePage(row)">新增</el-button>
                    </template>
                </el-table-column>
                <el-table-column property="orderNo" label="顺序" width="120">
                    <template #default="{ row }">
                        <el-input-number
                            v-model="row.orderNo"
                            size="small"
                            :min="0"
                            style="width: 100%"
                            placeholder="请输入顺序" />
                    </template>
                </el-table-column>
                <el-table-column property="type" label="导航类型" width="100">
                    <template #default="{ row }">
                        <el-select
                            v-model="row.type"
                            placeholder="请选择导航类型"
                            size="small"
                            clearable
                            style="width: 100%">
                            <el-option
                                v-for="item in enumStore.getNameCodeNumberOptionsByKey('navType')"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column property="menuType" label="菜单类型" width="100">
                    <template #default="{ row }">
                        <el-select
                            v-model="row.menuType"
                            placeholder="请选择菜单类型"
                            size="small"
                            clearable
                            style="width: 100%">
                            <el-option
                                v-for="item in enumStore.getNameCodeNumberOptionsByKey('menuType')"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column property="defaultPageFlag" label="默认页面" width="100">
                    <template #default="{ row }">
                        <el-switch v-model="row.defaultPageFlag" />
                    </template>
                </el-table-column>

                <el-table-column
                    v-for="imageType in navImageTypes"
                    :key="imageType.code"
                    :label="imageType.name"
                    width="180">
                    <template #default="{ row }">
                        <image-editor v-model="row.icons[imageType.code]" />
                    </template>
                </el-table-column>
                <el-table-column property="ruleCode" label="推荐策略" width="180" show-overflow-tooltip>
                    <template #default="{ row }">
                        <el-button text type="primary" size="small" @click="onClickSelectRule(row)">
                            {{
                                personalRuleList.find((item: any) => item.code === row.ruleCode)?.name || '选择推荐策略'
                            }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column property="inverted" label="是否反选" width="100">
                    <template #default="{ row }">
                        <div>{{ row.inverted ? '是' : '否' }}</div>
                    </template>
                </el-table-column>
                <el-table-column property="orgId" label="组织" width="180">
                    <template #default="{ row }">
                        <dimension-selector
                            v-model="row.orgId"
                            placeholder="请选择组织"
                            :data="orgTree"
                            label-key="name"
                            value-key="id"
                            width="370" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="300">
                    <template #default="{ row }">
                        <el-button type="primary" text @click="handleSaveNav(row)">保存</el-button>
                        <el-button text @click="handleCancelEdit(row)">取消</el-button>
                        <el-button type="danger" text @click="onClickDelete(row)">删除</el-button>
                        <el-button
                            :disabled="
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.ADD_CHILD_NAV, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-if="row.type === 1"
                            type="primary"
                            text
                            @click="onClickAddChildNav(row)">
                            新增子导航
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <personal-rule-dialog
                v-model="selectedRuleCode"
                v-model:dialog-visible="personalRuleDialogVisible"
                @save="savePersonalRule"
                v-if="personalRuleDialogVisible" />
            <nav-add-page v-model:visible="addPageValue" @confirm="handleAddPage" />
        </div>
    </el-drawer>
</template>

<script setup lang="ts">
    import { onMounted, PropType, ref, watch, computed } from 'vue';
    import { Nav, NavForm, NavSearchForm, Page, PersonalRule, Dimension } from '@smartdesk/common/types';
    import { format } from 'date-fns';
    import { dimensionApi, navApi, pageApi, personalRuleApi, publishApi } from '@smartdesk/common/api';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { useFeedback, useJumpToDesigner } from '@smartdesk/common/composables';
    import { Enumeration, LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDelete,
        canDisable,
        canBatchAudit,
        canEdit,
        canEnable,
        canOffline,
        canOnline,
    } from '@smartdesk/common/permission';
    import { FormInstance } from 'element-plus';
    import NavAddPage from './nav-add-page.vue';
    import { extractDimensions } from '@smartdesk/common/utils';
    import { CircleCheck } from '@element-plus/icons-vue';

    defineOptions({
        name: 'NavListAdd',
    });

    // 参数
    const props = defineProps({
        addNavVisible: {
            type: Object as PropType<boolean>,
            default: false,
        },
        sectionForm: {
            type: Object as PropType<Partial<NavSearchForm>>,
            default: () => ({}),
        },
        desktop: {
            type: Object as PropType<Page>,
            required: true,
        },
    });

    // 事件
    const emits = defineEmits(['update:selection', 'edit', 'update:addNavVisible', 'addNavVisibleClose']);

    // 网站 pinia
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();
    const { jumpToDesigner } = useJumpToDesigner();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    const selectedNavs = ref<NavForm[]>([]);

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 默认页面
    const defaultPage: Partial<Page> = {
        siteCode: siteStore.currentSite?.code,
        name: '',
        resolution: '',
    };

    // 默认导航表单
    const defaultNavForm: Partial<NavForm> = {
        siteCode: siteStore.currentSite?.code,
        ownerPageCode: props.desktop.code,
        type: 0,
        icons: {},
        page: defaultPage,
    };

    // 导航列表
    const navList = ref<NavForm[]>([]);

    // 页面列表
    const pageList = ref<Page[]>([]);

    // 推荐策略列表
    const personalRuleList = ref<PersonalRule[]>([]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 图片类型
    const navImageTypes = ref<Enumeration[]>(enumStore.getEnumsByKey('navImageTypeEnum') || []);

    const addVisible = ref(props.addNavVisible);

    // 编辑相关逻辑
    const originalRowData = ref<Record<string, any>>({});

    // 校验函数
    const validateNav = (row: Nav): boolean => {
        if (!row.title || !row.title.trim()) {
            feedback.error('导航标题不能为空');
            return false;
        }
        if (row.type === undefined || row.type === null) {
            feedback.error('导航类型不能为空');
            return false;
        }
        if (row.menuType === undefined || row.menuType === null) {
            feedback.error('菜单类型不能为空');
            return false;
        }
        // if (!row.pageCode || !row.pageCode.trim()) {
        //     feedback.error('页面名称不能为空');
        //     return false;
        // }
        if (row.orderNo === undefined || row.orderNo === null) {
            feedback.error('顺序不能为空');
            return false;
        }
        return true;
    };

    // 多选事件
    const handleSelectionChange = (val: NavForm[]) => {
        selectedNavs.value = val;
    };

    const codes = computed(() => {
        return selectedNavs.value.map((item: Nav) => item.code) ?? [];
    });

    // 点击批量送审
    const onClickBatchPublish = async () => {
        if (await feedback.confirm('确定要批量送审导航吗？', '确认操作', 'warning')) {
            const response = await publishApi.batchPublishSelf('Nav', codes.value, 'CREATE');
            if (response.code === 200) {
                feedback.success('批量送审成功');
                await getNavTree();
            } else {
                feedback.error('批量送审失败：' + response.msg);
            }
        }
    };

    const handleSaveNav = async (row: Nav) => {
        if (!validateNav(row)) return;
        handleNavSubmit(row);
    };
    // 处理导航提交事件
    const handleNavSubmit = async (navForm: Partial<NavForm>) => {
        console.log('🚀 ~ handleNavSubmit ~ navForm:', navForm);
        if (navForm.id && navForm.code) {
            // 走更新
            const res = await navApi.updateNav(navForm.code, navForm);
            if (res.code === 200) {
                await getNavTree();
                feedback.success('更新导航成功');
            } else {
                feedback.error('更新导航失败');
            }
        } else {
            // 走新增
            const res = await navApi.createNav(navForm);
            if (res.code === 200) {
                await getNavTree();
                feedback.success('新增导航成功');
            } else {
                feedback.error('新增导航失败');
            }
        }
    };

    // 取消编辑
    const handleCancelEdit = (row: Nav) => {
        const original = originalRowData.value[row.code];
        // 递归查找并替换
        const restoreNav = (nodes: Nav[]) => {
            for (let i = 0; i < nodes.length; i++) {
                if (nodes[i].code === row.code) {
                    nodes[i] = JSON.parse(JSON.stringify(original));
                    return true;
                }
                if (nodes[i].children && nodes[i].children.length > 0) {
                    if (restoreNav(nodes[i].children)) return true;
                }
            }
            return false;
        };

        restoreNav(navList.value);
    };

    // 推荐策略弹窗逻辑同前
    const selectedRuleCode = ref<string>();
    const personalRuleDialogVisible = ref<boolean>(false);
    const editingRuleRow = ref<any>(null);

    const onClickSelectRule = (row: any) => {
        selectedRuleCode.value = row.ruleCode;
        editingRuleRow.value = row;
        personalRuleDialogVisible.value = true;
    };
    const savePersonalRule = (code: string) => {
        if (editingRuleRow.value) {
            editingRuleRow.value.ruleCode = code;
        }
        personalRuleDialogVisible.value = false;
    };

    // 新增页面弹窗
    const addPageValue = ref<boolean>(false);

    // 单前新增导航表单
    const currentNavForm = ref<NavForm>({} as NavForm);

    // 点击新增页面按钮
    const handleSavePage = (row: NavForm) => {
        currentNavForm.value = row;
        addPageValue.value = true;
    };

    // 新增页面弹窗确定
    const handleAddPage = (data: Page) => {
        console.log('新增页面数据', data);
        // 可执行保存逻辑
        currentNavForm.value.page = data;

        if (currentNavForm.value.page) {
            const rect = extractDimensions(currentNavForm.value.page.resolution ?? '');
            currentNavForm.value.page.layout = {
                component: 'page',
                rect: {
                    top: 0,
                    left: 0,
                    width: rect ? rect.width : 0,
                    height: rect ? rect.height : 0,
                },
            } as any;
        }
        console.log('🚀 ~ handleAddPage ~ currentNavForm.value:', currentNavForm.value);
        onAddPageConfirm();
    };

    // 确定新增页面
    const onAddPageConfirm = () => {
        const targetCode = currentNavForm.value.code;
        const newPage = currentNavForm.value.page;

        // 递归查找并替换page
        const updatePage = (nodes: NavForm[]) => {
            for (let i = 0; i < nodes.length; i++) {
                if (nodes[i].code === targetCode) {
                    nodes[i].page = { ...newPage };
                    nodes[i].pageCode = '';
                    break;
                }
                if (nodes[i].children && nodes[i].children.length > 0) {
                    updatePage(nodes[i].children as any);
                }
            }
        };

        updatePage(navList.value);
    };

    /**
     * 生成临时唯一编码（用于未保存的导航）
     */
    const generateTempCode = (): string => {
        return `temp_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
    };
    /**
     * 创建基础导航对象（公共属性）
     * @param code 导航编码（临时或正式）
     * @param siteCode 站点编码
     * @param ownerPageCode 所属页面编码
     * @param extra 额外属性（如父级信息）
     */
    const createBaseNav = (
        code: string,
        siteCode: string,
        ownerPageCode: string,
        extra: Partial<NavForm> = {}
    ): NavForm =>
        ({
            code,
            siteCode,
            ownerPageCode,
            icons: {},
            title: '新导航',
            ...extra,
        }) as NavForm;

    /**
     * 添加顶级导航（根节点）
     */
    const onClickAdd = () => {
        // 生成临时编码并创建顶级导航对象
        const tempCode = generateTempCode();
        const newNav = createBaseNav(
            tempCode,
            props.sectionForm.siteCode || '',
            props.desktop.code,
            { title: '新导航', page: defaultPage } // 顶级导航默认标题
        );

        // 响应式添加至导航列表（假设navList是ref或响应式数组）
        navList.value.push(newNav);

        // 记录原始数据（深拷贝防止引用共享）
        originalRowData.value[tempCode] = JSON.parse(JSON.stringify(newNav));
    };

    /**
     * 添加子导航（需父导航上下文）
     * @param parentRow 父导航对象（需包含id/siteId/siteCode等必要信息）
     */
    const onClickAddChildNav = (parentRow: Nav) => {
        // 防御性校验：父导航必须存在且有效
        if (!parentRow?.id || !parentRow.siteId || !parentRow.siteCode) {
            feedback.error('无效的父导航，无法添加子导航');
            return;
        }

        // 生成临时编码并创建子导航对象
        const tempCode = generateTempCode();
        const newChild = createBaseNav(
            tempCode,
            parentRow.siteCode, // 继承父导航站点编码
            props.desktop.code,
            {
                parentId: parentRow.id!, // 父导航ID（非空断言，已通过校验）
                parentCode: parentRow.code!, // 父导航编码（非空断言）
                siteId: parentRow.siteId!, // 继承父导航站点ID（非空断言）
                title: '新子导航', // 子导航默认标题
                page: defaultPage, // 默认页面配置
            }
        );

        // 确保父导航children为响应式数组（兼容初始无children的情况）
        if (!parentRow.children) {
            parentRow.children = [] as Nav[]; // 类型断言（根据实际Nav类型调整）
        }

        // 响应式添加至父导航子列表
        parentRow.children.push(newChild);

        // 记录原始数据（深拷贝防止引用共享）
        originalRowData.value[tempCode] = JSON.parse(JSON.stringify(newChild));
    };
    /**
     * 删除导航的点击处理函数
     * @param row 当前操作的导航项
     */
    const onClickDelete = async (row: Nav) => {
        // 统一处理原始数据删除（无论新旧导航都需要）
        const handleOriginalDataDeletion = () => {
            delete originalRowData.value[row.code];
        };

        // 未保存导航的删除逻辑（无id的情况）
        const handleUnsavedNavDeletion = async () => {
            // 查找父导航（若不存在则使用空父对象）
            const parentNavItem = navList.value.find((item) => item.code === row.parentCode) ?? { children: [] };

            // 从父级children中移除当前导航（适用于有父级的场景）
            if (parentNavItem.children?.some((child) => child.code === row.code)) {
                parentNavItem.children = parentNavItem.children.filter((child) => child.code !== row.code);
                feedback.success('已移除未保存子导航');
                return true; // 标记已处理
            }

            // 无父级时从根列表移除（适用于顶级未保存导航）
            if (navList.value.some((item) => item.code === row.code)) {
                navList.value = navList.value.filter((item) => item.code !== row.code);
                feedback.success('已移除未保存导航');
                return true; // 标记已处理
            }

            return false; // 未找到需要删除的导航
        };

        // 已保存导航的删除逻辑（有id的情况）
        const handleSavedNavDeletion = async () => {
            const confirmRes = await feedback.confirm(`确定要删除该导航吗？`, '确认操作', 'warning');
            if (!confirmRes) return;

            try {
                const res = await publishApi.publishComplete('Nav', row.code, 'DELETE');
                if (res.code === 200) {
                    feedback.success('删除导航成功');
                    await getNavTree(); // 刷新导航树
                    return true;
                }
                throw new Error(res.msg || '未知错误');
            } catch (error) {
                feedback.error(`删除导航失败：${error instanceof Error ? error.message : '未知错误'}`);
                return false;
            }
        };

        // 主流程控制
        if (!row.id) {
            // 未保存导航处理
            const isHandled = await handleUnsavedNavDeletion();
            if (isHandled) {
                handleOriginalDataDeletion();
                return;
            }
            // 未找到可删除项时提示（原逻辑未覆盖此情况，新增防御性提示）
            feedback.error('未找到需要删除的未保存导航');
            return;
        }

        // 已保存导航处理
        const isHandled = await handleSavedNavDeletion();
        if (isHandled) {
            handleOriginalDataDeletion();
        }
    };

    // 定义类型转换函数（需要根据实际类型调整）
    const convertNavToForm = (nav: Nav): NavForm => {
        console.log('🚀 ~ convertNavToForm ~ nav:', nav);
        // 从已有页面列表中查找关联页面
        const targetPage = pageList.value.find((p) => p.code === nav.pageCode);

        return {
            ...nav,
            // 转换关联页面数据
            page: targetPage
                ? {
                      code: targetPage.code,
                      name: targetPage.name,
                      resolution: targetPage.resolution,
                      layout: targetPage.layout,
                  }
                : defaultPage, // 使用默认页面配置填充
            // 处理图标数据转换
            icons: nav.icons || ({} as any),
            // 转换组织ID数据
            orgId: nav.orgId ?? '',
        };
    };

    // 修改后的查询导航树方法
    const getNavTree = async () => {
        try {
            const response = await navApi.findNavTree(props.sectionForm);
            if (response.code === 200) {
                const processTree = (nodes: Nav[]) => {
                    nodes.forEach((node: Nav) => {
                        node.children = node.children || [];

                        // 转换原始Nav数据为NavForm结构
                        const convertedNode = convertNavToForm(node);

                        // 保存原始数据副本（深拷贝）
                        originalRowData.value[node.code] = JSON.parse(JSON.stringify(node));

                        // 更新树节点数据
                        Object.assign(node, convertedNode);

                        if (node.children.length > 0) {
                            processTree(node.children);
                        }
                    });
                };

                navList.value = response.result as any;
                processTree(navList.value);
            } else {
                feedback.error('获取导航树失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('获取导航树失败');
        }
    };

    // 查询页面列表
    const getPageList = async () => {
        const res = await pageApi.getPageList({ siteCode: siteStore.currentSiteCode }, { paged: false });
        if (res.code === 200) {
            pageList.value = res.result;
        }
    };

    // 查询推荐策略列表
    const getPersonalRuleList = async () => {
        const res = await personalRuleApi.getPersonalRuleList({}, { paged: false });
        if (res.code === 200) {
            personalRuleList.value = res.result;
        }
    };

    const onClose = () => {
        emits('addNavVisibleClose');
    };

    watch(
        () => props.sectionForm,
        (newVal) => {
            // 只有网站编码、桌面编码都存在时，才查询导航列表
            if (newVal.siteCode && newVal.desktopCode) {
                getNavTree();
            }
        },
        { deep: true, immediate: true }
    );

    watch(
        () => siteStore.currentSiteCode,
        () => {
            getPageList();
            getPersonalRuleList();
        },
        { deep: true, immediate: true }
    );

    watch(
        () => props.addNavVisible,
        (newVal) => {
            addVisible.value = newVal;
            if (newVal) {
                getNavTree();
            }
        }
    );

    onMounted(() => {
        getOrgTree();
    });

    defineExpose({
        getNavTree,
        getPageList,
    });
</script>
<style scoped>
    .nav-table-container {
        /* 确保容器高度有效 */
        min-height: calc(100vh - 120px);
    }
</style>
