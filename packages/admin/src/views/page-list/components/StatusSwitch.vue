<template>
    <el-switch
        v-model="localStatus"
        :active-value="1"
        :inactive-value="0"
        :disabled="disabled"
        :active-text="activeText"
        :inactive-text="inactiveText"
        @change="handleChange"
        size="small" />
</template>

<script setup lang="ts">
    import { ref, watch, defineProps, defineEmits } from 'vue';

    const props = defineProps({
        status: {
            type: Number,
            required: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        activeText: {
            type: String,
            default: '可用',
        },
        inactiveText: {
            type: String,
            default: '不可用',
        },
    });

    const emit = defineEmits(['change']);

    const localStatus = ref(props.status);

    watch(
        () => props.status,
        (val) => {
            localStatus.value = val;
        }
    );

    function handleChange(val: number) {
        emit('change', val);
    }
</script>
