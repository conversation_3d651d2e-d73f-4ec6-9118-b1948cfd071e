<template>
    <el-button
        :disabled="!permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.PAGE.CREATE)"
        type="primary"
        icon="Plus"
        @click="onCreatePage">
        新建页面
    </el-button>
    <el-button
        :disabled="
            !canBatchEnable(dataList) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.PAGE.BATCH_ENABLE, {
                type: 'org',
                value: dataList.filter((page) => page.orgId).map((page) => page.orgId),
            })
        "
        type="primary"
        icon="CircleCheck"
        @click="handleBatchAction('enable')">
        批量启用
    </el-button>
    <el-button
        :disabled="
            !canBatchDisable(dataList) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.PAGE.BATCH_DISABLE, {
                type: 'org',
                value: dataList.filter((page) => page.orgId).map((page) => page.orgId),
            })
        "
        type="warning"
        icon="CircleClose"
        @click="handleBatchAction('disable')">
        批量禁用
    </el-button>
    <el-button
        :disabled="
            !canBatchAudit(dataList) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.PAGE.AUDIT, {
                type: 'org',
                value: dataList.filter((page) => page.orgId).map((page) => page.orgId),
            })
        "
        type="primary"
        icon="Promotion"
        @click="handleBatchAction('audit')">
        批量送审
    </el-button>
    <el-button
        :disabled="
            !canBatchOnline(dataList) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.PAGE.BATCH_ONLINE, {
                type: 'org',
                value: dataList.filter((page) => page.orgId).map((page) => page.orgId),
            })
        "
        type="success"
        icon="Top"
        @click="handleBatchAction('online')">
        批量上线
    </el-button>
    <el-button
        :disabled="
            !canBatchOffline(dataList) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.PAGE.BATCH_OFFLINE, {
                type: 'org',
                value: dataList.filter((page) => page.orgId).map((page) => page.orgId),
            })
        "
        type="danger"
        icon="Bottom"
        @click="handleBatchAction('offline')">
        批量下线
    </el-button>
    <el-button
        :disabled="
            !canBatchDelete(dataList) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.PAGE.BATCH_DELETE, {
                type: 'org',
                value: dataList.filter((page) => page.orgId).map((page) => page.orgId),
            })
        "
        type="danger"
        icon="Delete"
        @click="handleBatchAction('delete')">
        批量删除
    </el-button>
</template>

<script setup lang="ts">
    import {
        ADMIN_BIZ_PERMISSION,
        canBatchAudit,
        canBatchDelete,
        canBatchDisable,
        canBatchEnable,
        canBatchOffline,
        canBatchOnline,
    } from '@smartdesk/common/permission';
    import { Page } from '@smartdesk/common/types';
    import { PropType } from 'vue';
    import { usePermissionStore } from '@chances/portal_common_core';

    // 参数
    const props = defineProps({
        dataList: {
            type: Object as PropType<Page[]>,
            required: true,
        },
    });

    // 事件
    const emits = defineEmits(['batch-action', 'create-page']);

    // pinia store
    const permissionStore = usePermissionStore();

    // 点击批量区按钮
    const handleBatchAction = (action: string) => {
        emits('batch-action', action);
    };

    // 点击新增页面按钮
    const onCreatePage = () => {
        emits('create-page');
    };
</script>
