<template>
    <BaseTableWithPagination
        :total="totalElements"
        :current-page="currentPage"
        :page-size="pageSize"
        @update:currentPage="handleCurrentChange"
        @update:pageSize="handleSizeChange">
        <template #table>
            <el-table
                :data="tableData"
                row-key="id"
                :header-cell-style="{ color: 'black', height: '50px' }"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="name" label="页面名称" show-overflow-tooltip width="240" />
                <el-table-column label="业务分组" width="140">
                    <template #default="{ row }">
                        <el-tag v-if="row.bizGroup" type="success">
                            {{ enumStore.getLabelByKeyAndValue('bizGroup', row.bizGroup) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="resolution" label="分辨率" width="120">
                    <template #default="{ row }">
                        <el-tag type="primary"
                            >{{ enumStore.getLabelByKeyAndValue('resolution', row.resolution) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column property="orgId" label="组织" width="180">
                    <template #default="{ row }">
                        <div>
                            {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="可用状态" width="120">
                    <template #default="{ row }">
                        <el-switch
                            v-if="row.status === 1"
                            :disabled="
                                !canDisable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.COMPONENT.DISABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                        <el-switch
                            v-else
                            :disabled="
                                !canEnable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.COMPONENT.ENABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column label="状态" width="280">
                    <template #default="{ row }">
                        <status-columns :publishStatus="row" />
                    </template>
                </el-table-column>
                <el-table-column prop="modifiedTime" label="修改时间" width="180">
                    <template #default="{ row }">
                        {{ row.modifiedTime ? format(parseISO(row.modifiedTime), 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="modifiedBy" label="修改人" show-overflow-tooltip width="120" />
                <el-table-column label="操作" min-width="200" fixed="right">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-button link type="primary" @click="handleAction('config', row)"> 配置 </el-button>
                            <el-button
                                :disabled="
                                    !canAudit(row) ||
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PAGE.AUDIT, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                link
                                type="primary"
                                @click="handleAction('audit', row)">
                                送审
                            </el-button>
                            <el-button
                                :disabled="
                                    !canOnline(row) ||
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PAGE.ONLINE, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                v-if="row.onlineStatus === 0 || row.onlineStatus === 2"
                                link
                                type="success"
                                @click="handleAction('online', row)">
                                上线
                            </el-button>
                            <el-button
                                :disabled="
                                    !canOffline(row) ||
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PAGE.OFFLINE, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                v-if="row.onlineStatus === 1"
                                link
                                type="danger"
                                @click="handleAction('offline', row)">
                                下线
                            </el-button>
                            <el-dropdown class="ml-3" :hide-on-click="false">
                                <el-button link type="primary">
                                    更多
                                    <el-icon class="el-icon--right">
                                        <ArrowDown />
                                    </el-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item>
                                            <el-button link type="info" @click="handleAction('showPageSection', row)">
                                                查看节
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item>
                                            <el-button
                                                :disabled="
                                                    !canEdit(row) ||
                                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PAGE.EDIT, {
                                                        type: 'org',
                                                        value: row.orgId,
                                                    })
                                                "
                                                link
                                                type="warning"
                                                @click="handleAction('edit', row)">
                                                编辑
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item>
                                            <el-button
                                                :disabled="
                                                    !canDelete(row) ||
                                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PAGE.DELETE, {
                                                        type: 'org',
                                                        value: row.orgId,
                                                    })
                                                "
                                                link
                                                type="danger"
                                                @click="handleAction('delete', row)">
                                                删除
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="canEnable(row)">
                                            <el-button
                                                :disabled="
                                                    !canEnable(row) ||
                                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PAGE.ENABLE, {
                                                        type: 'org',
                                                        value: row.orgId,
                                                    })
                                                "
                                                link
                                                type="primary"
                                                @click="handleAction('enable', row)">
                                                启用
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="canDisable(row)">
                                            <el-button
                                                :disabled="
                                                    !canDisable(row) ||
                                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PAGE.DISABLE, {
                                                        type: 'org',
                                                        value: row.orgId,
                                                    })
                                                "
                                                link
                                                type="warning"
                                                @click="handleAction('disable', row)">
                                                禁用
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item>
                                            <el-button
                                                :disabled="
                                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PAGE.COPY, {
                                                        type: 'org',
                                                        value: row.orgId,
                                                    })
                                                "
                                                link
                                                type="primary"
                                                @click="handleAction('copy', row)">
                                                复制
                                            </el-button>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </BaseTableWithPagination>
</template>

<script setup lang="ts">
    import { Page, PageSearchForm } from '@smartdesk/common/types';
    import { format, parseISO } from 'date-fns';
    import { onMounted, ref, watch } from 'vue';
    import { dimensionApi, pageApi } from '@smartdesk/common/api';
    import { LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';
    import { useFeedback } from '@smartdesk/common/composables';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDelete,
        canDisable,
        canEdit,
        canEnable,
        canOffline,
        canOnline,
    } from '@smartdesk/common/permission';
    import { ArrowDown } from '@element-plus/icons-vue';

    // 参数
    const props = defineProps<{
        searchFrom: PageSearchForm;
    }>();

    // 事件
    const emits = defineEmits(['selection-change', 'row-action']);

    // pinia store
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 页面列表
    const tableData = ref<Page[]>([] as Page[]);

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        findPageList();
    };

    // 处理当前页变化
    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        findPageList();
    };

    // 查询页面
    const findPageList = async () => {
        const response = await pageApi.getPageList(props.searchFrom, {
            page: currentPage.value - 1,
            size: pageSize.value,
            sort: 'id,desc',
        });
        if (response.code === 200) {
            tableData.value = response.result;
            totalElements.value = Number(response.page.totalElements);
        }
    };

    // 多选
    const handleSelectionChange = (selection: Page[]) => {
        emits('selection-change', selection);
    };

    // 操作
    const handleAction = (action: string, row: Page) => {
        emits('row-action', action, row);
    };

    // 启用/禁用网站
    const handleStatusChange = async (row: Page) => {
        const newStatus = row.status;
        const originalStatus = newStatus === 1 ? 0 : 1;
        const confirmed = await feedback.confirm(
            `确定要${newStatus === 0 ? '不可用' : '可用'}该页面吗？`,
            '确定操作',
            'warning'
        );
        if (confirmed) {
            if (newStatus === 0) {
                emits('row-action', 'disable', row);
            } else {
                emits('row-action', 'enable', row);
            }
        } else {
            // 用户取消，恢复原状态
            row.status = originalStatus;
        }
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    watch(
        () => props.searchFrom,
        () => {
            findPageList();
        }
    );

    onMounted(() => {
        getOrgOptions();
    });

    // 公开方法给父组件调用
    defineExpose({
        findPageList,
    });
</script>
