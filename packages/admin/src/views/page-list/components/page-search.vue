<template>
    <site-selector />
    <el-form inline label-width="100" :label-suffix="':'" :size="'default'" class="mt-2">
        <search-container
            :initialWidth="315"
            :searchForm="searchForm"
            :isValueNotEmpty="isValueNotEmpty"
            :getKeyFormat="getKeyFormat"
            :getValueFormat="getValueFormat"
            :handleTagClose="handleTagClose"
            :orgOptions="orgOptions"
            :activeFields="activeFields">
            <template #item-0>
                <el-form-item label="标签">
                    <el-input
                        v-model="searchForm.tags"
                        @update:modelValue="(val: any) => handleFieldUpdate('tags', val)"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable />
                </el-form-item>
            </template>
            <template #item-1>
                <el-form-item label="业务分组">
                    <el-select
                        v-model="searchForm.bizGroups"
                        @update:modelValue="(val: any) => handleFieldUpdate('bizGroups', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in bizGroupOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-2>
                <el-form-item label="组织">
                    <el-tree-select
                        v-model="searchForm.orgIds"
                        @update:modelValue="(val: any) => handleFieldUpdate('orgIds', val)"
                        placeholder="请选择"
                        :data="orgTree"
                        :props="treeProps"
                        filterable
                        multiple
                        :render-after-expand="false"
                        collapse-tags
                        collapse-tags-tooltip
                        show-checkbox
                        check-strictly
                        check-on-click-node
                        style="width: 180px"
                        node-key="id"
                        value-key="id" />
                </el-form-item>
            </template>
            <template #item-3>
                <el-form-item label="关键词">
                    <el-input
                        v-model="searchForm.name"
                        @update:modelValue="(val: any) => handleFieldUpdate('name', val)"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </template>
            <template #item-4>
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.statuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('statuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in enableStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-5>
                <el-form-item label="上下线状态">
                    <el-select
                        v-model="searchForm.onlineStatuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('onlineStatuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in onlineStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-6>
                <el-form-item label="审核状态">
                    <el-select
                        v-model="searchForm.auditStatuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('auditStatuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in auditStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-7>
                <el-form-item label="可见状态">
                    <el-select
                        v-model="searchForm.visibleStatuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('visibleStatuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in visibleStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-8>
                <el-form-item label="删除状态">
                    <el-select
                        v-model="searchForm.delFlags"
                        @update:modelValue="(val: any) => handleFieldUpdate('delFlags', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in delFlagOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
        </search-container>
    </el-form>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { Dimension, PageSearchForm } from '@smartdesk/common/types';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { dimensionApi } from '@smartdesk/common/api';
    import SearchContainer from '@smartdesk/admin/views/common/search-container.vue';
    import { getKeyFormat, getValueFormat, handleTagClose, isValueNotEmpty } from '@smartdesk/common/utils';

    // 事件
    const emit = defineEmits(['search-page']);

    // pinia store
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 删除状态枚举
    const delFlagOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    // 审核状态枚举
    const auditStatusOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('auditStatus') || []);

    // 可用禁用状态枚举
    const enableStatusOption = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 可见枚举
    const visibleStatusOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('visibleStatus') || []);

    //  业务分组
    const bizGroupOptions = ref<
        {
            label: string;
            value: string;
        }[]
    >(enumStore.getOptionsByKey('bizGroup') || []);

    // tree配置
    const treeProps = {
        children: 'children',
        label: 'name',
        value: 'id',
    };

    // 搜索表单
    const searchForm = ref<Partial<PageSearchForm>>({
        siteCode: '',
        tags: '',
        bizGroups: [],
        orgIds: [],
        name: '',
        statuses: [],
        onlineStatuses: [],
        auditStatuses: [],
        visibleStatuses: [],
        delFlags: [0] as number[],
    });

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 用户设置表单字段的顺序
    const activeFields = ref<string[]>([]);

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    function handleFieldUpdate(field: keyof PageSearchForm, value: any) {
        searchForm.value[field] = value;

        // 判断值是否有效（非空/非空数组）
        const isEmpty =
            value === '' || value === null || value === undefined || (Array.isArray(value) && value.length === 0);

        if (!isEmpty) {
            if (!activeFields.value.includes(field as string)) {
                activeFields.value.push(field as string);
            }
        } else {
            // 如果用户清空值了，移除 field
            const index = activeFields.value.indexOf(field as string);
            if (index !== -1) {
                activeFields.value.splice(index, 1);
            }
        }
    }

    // 上线枚举
    const onlineStatusOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('onlineStatus') || []);

    // 条件查询页面列表
    const handleSearch = () => {
        emit('search-page', searchForm.value);
    };
    // 初始化字段
    const initActiveFields = () => {
        Object.entries(searchForm.value).forEach(([key, val]) => {
            if (isValueNotEmpty(val)) {
                if (!activeFields.value.includes(key)) {
                    activeFields.value.push(key);
                }
            }
        });
    };

    // 监听 siteStore
    watch(
        () => siteStore.currentSiteCode,
        () => {
            searchForm.value.siteCode = siteStore.currentSiteCode;
        }
    );

    // 监听查询表单
    watch(
        () => searchForm.value,
        () => {
            handleSearch();
        },
        { deep: true }
    );

    // 组件挂载时请求数据
    onMounted(() => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
        getOrgTree();
        getOrgOptions();
        initActiveFields();
    });
</script>
<style scoped>
    .tree-select-auto {
        min-width: 180px;
        max-width: 600px;
        width: auto;
    }
</style>
